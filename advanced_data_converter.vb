Sub AdvancedDataConverter()
    Dim wsSource As Worksheet
    Dim wsNew As Worksheet
    Dim ordersRow As Long
    Dim lastCol As Long
    Dim sourceRange As Range
    Dim response As VbMsgBoxResult
    
    ' التحقق من وجود Sheet1
    On Error Resume Next
    Set wsSource = ThisWorkbook.Sheets("Sheet1")
    On Error GoTo 0
    
    If wsSource Is Nothing Then
        MsgBox "لم يتم العثور على Sheet1", vbCritical
        Exit Sub
    End If
    
    ' البحث عن كلمة "Orders"
    ordersRow = FindOrdersRow(wsSource)
    
    If ordersRow = 0 Then
        MsgBox "لم يتم العثور على كلمة 'Orders' في الشيت", vbExclamation
        Exit Sub
    End If
    
    ' تأكيد من المستخدم
    response = MsgBox("تم العثور على كلمة 'Orders' في الصف " & ordersRow & vbCrLf & _
                     "هل تريد المتابعة لإنشاء شيت جديد؟", vbYesNo + vbQuestion)
    
    If response = vbNo Then Exit Sub
    
    ' إنشاء الشيت الجديد
    Set wsNew = CreateNewSheet()
    
    ' نسخ وتحويل البيانات
    CopyAndConvertData wsSource, wsNew, ordersRow
    
    ' تنسيق الشيت الجديد
    FormatConvertedSheet wsNew, ordersRow
    
    ' رسالة النجاح
    MsgBox "تم إنشاء الشيت الجديد بنجاح!" & vbCrLf & _
           "اسم الشيت: " & wsNew.Name & vbCrLf & _
           "البيانات المنسوخة: A1:O" & ordersRow & vbCrLf & _
           "تم تحويل النصوص إلى أرقام حيث أمكن", vbInformation
    
    ' تفعيل الشيت الجديد
    wsNew.Activate
    wsNew.Range("A1").Select
    
End Sub

' دالة للبحث عن صف "Orders"
Function FindOrdersRow(ws As Worksheet) As Long
    Dim i As Long, j As Long
    Dim cellValue As String
    
    FindOrdersRow = 0
    
    ' البحث في الصفوف من 1 إلى 1000 (يمكن تعديل هذا الرقم)
    For i = 1 To 1000
        For j = 1 To 15 ' من العمود A إلى O
            cellValue = Trim(CStr(ws.Cells(i, j).Value))
            If InStr(1, cellValue, "Orders", vbTextCompare) > 0 Then
                FindOrdersRow = i
                Exit Function
            End If
        Next j
    Next i
End Function

' دالة لإنشاء شيت جديد
Function CreateNewSheet() As Worksheet
    Dim newSheetName As String
    Dim counter As Integer
    
    counter = 1
    newSheetName = "ProcessedData"
    
    ' التأكد من عدم وجود شيت بنفس الاسم
    Do While SheetExists(newSheetName)
        newSheetName = "ProcessedData_" & counter
        counter = counter + 1
    Loop
    
    Set CreateNewSheet = ThisWorkbook.Sheets.Add
    CreateNewSheet.Name = newSheetName
End Function

' دالة للتحقق من وجود شيت
Function SheetExists(sheetName As String) As Boolean
    Dim ws As Worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets(sheetName)
    SheetExists = Not ws Is Nothing
    On Error GoTo 0
End Function

' دالة لنسخ وتحويل البيانات
Sub CopyAndConvertData(wsSource As Worksheet, wsTarget As Worksheet, ordersRow As Long)
    Dim i As Long, j As Long
    Dim cellValue As Variant
    Dim convertedValue As Variant
    
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    ' نسخ البيانات مع التحويل
    For i = 1 To ordersRow
        For j = 1 To 15 ' من العمود A إلى O
            cellValue = wsSource.Cells(i, j).Value
            convertedValue = ConvertCellValue(cellValue)
            wsTarget.Cells(i, j).Value = convertedValue
        Next j
    Next i
    
    Application.Calculation = xlCalculationAutomatic
    Application.ScreenUpdating = True
End Sub

' دالة لتحويل قيمة الخلية
Function ConvertCellValue(cellValue As Variant) As Variant
    Dim tempValue As String
    Dim numericValue As Double
    
    ' إذا كانت الخلية فارغة
    If IsEmpty(cellValue) Or cellValue = "" Then
        ConvertCellValue = ""
        Exit Function
    End If
    
    tempValue = Trim(CStr(cellValue))
    
    ' محاولة تحويل إلى رقم
    If IsNumeric(tempValue) And tempValue <> "" Then
        numericValue = CDbl(tempValue)
        ConvertCellValue = numericValue
    ElseIf IsDate(tempValue) Then
        ' تحويل إلى تاريخ
        ConvertCellValue = CDate(tempValue)
    Else
        ' الاحتفاظ بالنص كما هو
        ConvertCellValue = tempValue
    End If
End Function

' دالة لتنسيق الشيت المحول
Sub FormatConvertedSheet(ws As Worksheet, lastRow As Long)
    Dim headerRange As Range
    Dim dataRange As Range
    
    ' تحديد النطاقات
    Set headerRange = ws.Range("A1:O1")
    Set dataRange = ws.Range("A1:O" & lastRow)
    
    ' تنسيق عام للبيانات
    With dataRange
        .Font.Name = "Calibri"
        .Font.Size = 11
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
        .Borders.Color = RGB(200, 200, 200)
    End With
    
    ' تنسيق العناوين
    With headerRange
        .Font.Bold = True
        .Font.Color = RGB(255, 255, 255)
        .Interior.Color = RGB(68, 114, 196)
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With
    
    ' ضبط عرض الأعمدة
    ws.Columns("A:O").AutoFit
    
    ' إضافة تصفية تلقائية
    If lastRow > 1 Then
        dataRange.AutoFilter
    End If
    
    ' تجميد الصف الأول
    ws.Range("A2").Select
    ActiveWindow.FreezePanes = True
    
End Sub

' دالة سريعة للتشغيل المباشر
Sub QuickConvert()
    Call AdvancedDataConverter
End Sub
