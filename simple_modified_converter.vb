Sub SimpleModifiedConverter()
    ' نسخة مبسطة من الكود المعدل
    
    Dim ws1 As Worksheet, ws2 As Worksheet
    Dim ordersRow As Long, lastRowToCopy As Long
    Dim foundCell As Range
    Dim i As Long, j As Long
    
    ' تعيين Sheet1
    Set ws1 = ThisWorkbook.Sheets("Sheet1")
    
    ' البحث عن كلمة "Orders"
    Set foundCell = ws1.Range("A:O").Find("Orders", , xlValues, xlPart, , , False)
    
    If foundCell Is Nothing Then
        MsgBox "لم يتم العثور على كلمة Orders"
        Exit Sub
    End If
    
    ordersRow = foundCell.Row
    lastRowToCopy = ordersRow - 1 ' الصف الذي يسبق Orders
    
    If lastRowToCopy < 1 Then
        MsgBox "لا توجد بيانات للنسخ قبل صف Orders"
        Exit Sub
    End If
    
    ' إنشاء شيت جديد
    Set ws2 = Sheets.Add
    ws2.Name = "ModifiedData"
    
    ' نسخ البيانات مع التحويل (بدون صف Orders)
    For i = 1 To lastRowToCopy
        For j = 1 To 15 ' A إلى O
            Dim cellVal As Variant
            cellVal = ws1.Cells(i, j).Value
            
            ' تحويل النص إلى رقم إذا أمكن
            If IsNumeric(cellVal) And cellVal <> "" Then
                ws2.Cells(i, j).Value = CDbl(cellVal)
            Else
                ws2.Cells(i, j).Value = cellVal
            End If
        Next j
    Next i
    
    ' حذف العمودين M و N
    Application.DisplayAlerts = False
    ws2.Columns("N:N").Delete ' حذف العمود N أولاً
    ws2.Columns("M:M").Delete ' ثم حذف العمود M
    Application.DisplayAlerts = True
    
    ' إضافة كلمة "Profit" في الخلية M8
    ws2.Range("M8").Value = "Profit"
    
    ' تنسيق بسيط
    ws2.Range("A1:M1").Font.Bold = True
    ws2.Range("A1:M1").Interior.Color = RGB(200, 220, 255)
    
    ' تنسيق خاص لخلية Profit
    With ws2.Range("M8")
        .Font.Bold = True
        .Font.Color = RGB(0, 128, 0)
        .Interior.Color = RGB(220, 255, 220)
        .HorizontalAlignment = xlCenter
    End With
    
    ws2.Columns("A:M").AutoFit
    
    MsgBox "تم التحويل بنجاح!" & vbCrLf & _
           "الشيت الجديد: " & ws2.Name & vbCrLf & _
           "تم نسخ " & lastRowToCopy & " صف" & vbCrLf & _
           "تم حذف العمودين M و N" & vbCrLf & _
           "تم إضافة Profit في M8"
    
    ws2.Activate
    
End Sub

' دالة للتحويل مع تنسيق متقدم
Sub AdvancedModifiedConverter()
    Dim ws1 As Worksheet, ws2 As Worksheet
    Dim ordersRow As Long, lastRowToCopy As Long
    Dim sourceRange As Range, targetRange As Range
    Dim foundCell As Range
    
    Set ws1 = ThisWorkbook.Sheets("Sheet1")
    
    ' البحث عن "Orders"
    Set foundCell = ws1.Range("A:O").Find("Orders", , xlValues, xlPart)
    
    If foundCell Is Nothing Then
        MsgBox "كلمة Orders غير موجودة"
        Exit Sub
    End If
    
    ordersRow = foundCell.Row
    lastRowToCopy = ordersRow - 1
    
    If lastRowToCopy < 1 Then
        MsgBox "لا توجد بيانات للنسخ"
        Exit Sub
    End If
    
    ' إنشاء شيت جديد
    Set ws2 = Sheets.Add
    ws2.Name = "AdvancedModified_" & Format(Now, "hhmmss")
    
    ' تحديد النطاق المصدر والهدف (بدون صف Orders)
    Set sourceRange = ws1.Range("A1:O" & lastRowToCopy)
    Set targetRange = ws2.Range("A1:O" & lastRowToCopy)
    
    ' نسخ القيم والتنسيق
    sourceRange.Copy
    targetRange.PasteSpecial xlPasteValues
    targetRange.PasteSpecial xlPasteFormats
    
    ' تحويل النصوص إلى أرقام
    Dim cell As Range
    For Each cell In targetRange
        If IsNumeric(cell.Value) And cell.Value <> "" Then
            cell.Value = CDbl(cell.Value)
        End If
    Next cell
    
    Application.CutCopyMode = False
    
    ' حذف العمودين M و N
    Application.DisplayAlerts = False
    ws2.Columns("N:N").Delete
    ws2.Columns("M:M").Delete
    Application.DisplayAlerts = True
    
    ' إضافة "Profit" في M8
    ws2.Range("M8").Value = "Profit"
    
    ' تحسين التنسيق
    With ws2.Range("A1:M" & lastRowToCopy)
        .Borders.LineStyle = xlContinuous
        .Font.Name = "Arial"
        .Font.Size = 10
    End With
    
    ' تنسيق الصف الأول
    With ws2.Range("A1:M1")
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196)
        .Font.Color = RGB(255, 255, 255)
        .HorizontalAlignment = xlCenter
    End With
    
    ' تنسيق خلية Profit
    With ws2.Range("M8")
        .Font.Bold = True
        .Font.Size = 12
        .Font.Color = RGB(0, 128, 0)
        .Interior.Color = RGB(220, 255, 220)
        .HorizontalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlMedium
        .Borders.Color = RGB(0, 128, 0)
    End With
    
    ws2.Columns("A:M").AutoFit
    
    ' إضافة تصفية
    If lastRowToCopy > 1 Then
        ws2.Range("A1:M" & lastRowToCopy).AutoFilter
    End If
    
    MsgBox "تم إنشاء " & ws2.Name & " بنجاح مع التنسيق المتقدم" & vbCrLf & _
           "البيانات: من الصف 1 إلى " & lastRowToCopy & vbCrLf & _
           "تم حذف العمودين M و N وإضافة Profit في M8"
    
    ws2.Activate
    
End Sub
