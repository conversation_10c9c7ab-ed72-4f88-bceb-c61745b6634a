' ===== الجزء الثاني: يوضع في UserForm =====

' الزر الأول - CommandButton1
Private Sub CommandButton1_Click()
    Dim salary As Double
    Dim totalResult As Double
    
    If Not IsNumeric(Replace(Me.TextBox10.Value, "$", "")) Then
        ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48
        Exit Sub
    End If
    
    If Trim(Me.TextBox11.Value) = "" Or Not IsNumeric(Replace(Me.TextBox11.Value, " Lot", "")) Then
        ShowArabicMessage CreateArabicText("enter_volume"), CreateArabicText("warning_title"), 48
        Me.TextBox9.Value = ""
        Exit Sub
    End If
    
    If CDbl(Replace(Me.TextBox11.Value, " Lot", "")) < 100 Then
        ShowArabicMessage CreateArabicText("no_salary"), CreateArabicText("error_title"), 16
        Me.TextBox9.Value = ""
        Exit Sub
    End If
    
    salary = CDbl(Replace(Me.TextBox10.Value, "$", ""))
    totalResult = 0
    
    ' الحسابات باستخدام النصوص العربية
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("daily_post") & ": 30 / 30 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("withdrawal_proof") & ": 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("enzo_reasons") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("live_webinar") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.1)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("interactive_reels") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.08)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("educational_courses") & ": 5 / 5 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("weekly_analysis") & ": 4 / 4 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("withdrawal_bonus") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("company_cooperation") & ": 1 / 1 ") > 0 Then totalResult = totalResult + (salary * 0.1)
    
    Me.TextBox9.Value = Format(totalResult, "$#,##0.00")
End Sub

' الزر الثاني - CommandButton2
Private Sub CommandButton2_Click()
    Dim txt As String
    Dim val5 As Double, val6 As Double
    Dim totalValue As Double
    Dim selectedRank As String
    
    txt = Me.TextBox7.Value
    totalValue = 0
    selectedRank = Me.ComboBox1.Value
    
    If Trim(Me.TextBox12.Value) = "" Or Not IsNumeric(Replace(Me.TextBox12.Value, "$", "")) Then
        ShowArabicMessage CreateArabicText("select_daily_level"), CreateArabicText("error_title"), 16
        Me.TextBox12.SetFocus
        Exit Sub
    End If
    
    val5 = CDbl(Replace(Me.TextBox12.Value, "$", ""))
    
    If IsNumeric(Replace(Me.TextBox13.Value, "$", "")) Then
        val6 = CDbl(Replace(Me.TextBox13.Value, "$", ""))
    Else
        val6 = 0
    End If
    
    If InStr(txt, "- " & CreateArabicText("daily_post") & ": 30 / 30") > 0 Then
        totalValue = val5
    End If
    
    If Not (selectedRank = "Beginning" Or selectedRank = "Growth") Then
        If InStr(txt, "- " & CreateArabicText("weekly_analysis") & ": 1 / 4") > 0 Then totalValue = totalValue + val6
        If InStr(txt, "- " & CreateArabicText("weekly_analysis") & ": 2 / 4") > 0 Then totalValue = totalValue + (2 * val6)
        If InStr(txt, "- " & CreateArabicText("weekly_analysis") & ": 3 / 4") > 0 Then totalValue = totalValue + (3 * val6)
        If InStr(txt, "- " & CreateArabicText("weekly_analysis") & ": 4 / 4") > 0 Then totalValue = totalValue + (4 * val6)
    End If
    
    Me.TextBox8.Value = Format(totalValue, "$#,##0.00")
End Sub

' الزر الثالث - CommandButton3 (المحدث)
Private Sub CommandButton3_Click()
    Dim inputValue As Double
    Dim resultValue As Double
    Dim multiplier As Long
    Dim val As String
    
    ' التحقق الأول: اختيار نوع الوكالة في ComboBox4
    If Trim(Me.ComboBox4.Value) = "" Then
        ShowArabicMessage CreateArabicText("select_agency_type"), CreateArabicText("warning_title"), 48
        Me.ComboBox4.SetFocus
        Exit Sub
    End If
    
    ' التحقق الثاني: اختيار مرتبة الوكيل في ComboBox5
    If Trim(Me.ComboBox5.Value) = "" Then
        ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48
        Me.ComboBox5.SetFocus
        Exit Sub
    End If
    
    ' التحقق الثالث: إدخال رقم صحيح في TextBox17
    val = Replace(Me.TextBox17.Value, " Lot", "")
    If Trim(val) = "" Or Not IsNumeric(val) Then
        ShowArabicMessage CreateArabicText("enter_correct_number"), CreateArabicText("warning_title"), 48
        Me.TextBox17.SetFocus
        Exit Sub
    End If
    
    inputValue = CDbl(val)
    
    ' الحسابات حسب المرتبة المختارة
    Select Case Me.ComboBox5.Value
        Case "Pro"
            multiplier = Int(inputValue / 200)
            resultValue = multiplier * 100
        Case "Elite"
            multiplier = Int(inputValue / 300)
            resultValue = multiplier * 150
        Case "Bronze"
            If inputValue > 200 Then
                resultValue = (inputValue - 200) * 0.25
            Else
                resultValue = 0
            End If
        Case "Silver"
            If inputValue > 300 Then
                resultValue = (inputValue - 300) * 0.25
            Else
                resultValue = 0
            End If
        Case "Gold"
            If inputValue > 600 Then
                resultValue = (inputValue - 600) * 0.25
            Else
                resultValue = 0
            End If
        Case "Platinum"
            If inputValue > 1200 Then
                resultValue = (inputValue - 1200) * 0.25
            Else
                resultValue = 0
            End If
        Case "Diamond"
            If inputValue > 2500 Then
                resultValue = (inputValue - 2500) * 0.25
            Else
                resultValue = 0
            End If
        Case "Sapphire"
            If inputValue > 5000 Then
                resultValue = (inputValue - 5000) * 0.25
            Else
                resultValue = 0
            End If
        Case "Emerald"
            If inputValue > 10000 Then
                resultValue = (inputValue - 10000) * 0.25
            Else
                resultValue = 0
            End If
        Case "King"
            If inputValue > 20000 Then
                resultValue = (inputValue - 20000) * 0.25
            Else
                resultValue = 0
            End If
        Case "The Legend"
            If inputValue > 40000 Then
                resultValue = (inputValue - 40000) * 0.25
            Else
                resultValue = 0
            End If
        Case Else
            ' هذا Case لن يتم الوصول إليه لأننا تحققنا من ComboBox5 مسبقاً
            ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48
            Exit Sub
    End Select
    
    ' عرض النتيجة
    Me.TextBox15.Value = Format(resultValue, "$#,##0.00")
End Sub
