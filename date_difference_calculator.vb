Sub CopyDataAndCalculateDateDifference()
    ' كود لنسخ البيانات وحساب الفرق بين التواريخ
    
    Dim wsSource As Worksheet
    Dim wsNew As Worksheet
    Dim ordersRow As Long
    Dim lastRowToCopy As Long
    Dim foundCell As Range
    Dim i As Long
    Dim dateA As Date, dateI As Date
    Dim dateDifference As Long
    Dim cellValueA As String, cellValueI As String
    
    ' تعيين الشيت المصدر
    Set wsSource = ActiveSheet
    
    ' البحث عن كلمة "Orders"
    Set foundCell = wsSource.Range("A:O").Find("Orders", , xlValues, xlPart, , , False)
    
    If foundCell Is Nothing Then
        MsgBox "لم يتم العثور على كلمة 'Orders' في الشيت", vbExclamation
        Exit Sub
    End If
    
    ordersRow = foundCell.Row
    lastRowToCopy = ordersRow - 1
    
    If lastRowToCopy < 9 Then
        MsgBox "لا توجد بيانات كافية للمعالجة (يجب أن يكون هناك على الأقل 9 صفوف قبل Orders)", vbExclamation
        Exit Sub
    End If
    
    ' تأكيد من المستخدم
    Dim response As VbMsgBoxResult
    response = MsgBox("تم العثور على 'Orders' في الصف " & ordersRow & vbCrLf & _
                     "سيتم نسخ البيانات من A1 إلى O" & lastRowToCopy & vbCrLf & _
                     "وحساب الفرق بين التواريخ من الصف 9 إلى " & lastRowToCopy & vbCrLf & _
                     "هل تريد المتابعة؟", vbYesNo + vbQuestion)
    
    If response = vbNo Then Exit Sub
    
    ' إنشاء شيت جديد
    Set wsNew = Sheets.Add
    wsNew.Name = "ProcessedData_" & Format(Now, "yyyymmdd_hhmmss")
    
    ' نسخ البيانات من A1 إلى O + lastRowToCopy
    wsSource.Range("A1:O" & lastRowToCopy).Copy
    wsNew.Range("A1").PasteSpecial xlPasteValues
    wsNew.Range("A1").PasteSpecial xlPasteFormats
    Application.CutCopyMode = False
    
    ' إضافة عمود جديد للفرق بين التواريخ
    wsNew.Range("P8").Value = "Date Difference (Days)"
    wsNew.Range("P8").Font.Bold = True
    wsNew.Range("P8").Interior.Color = RGB(255, 255, 0) ' خلفية صفراء
    wsNew.Range("P8").HorizontalAlignment = xlCenter
    
    ' حساب الفرق بين التواريخ من الصف 9 إلى lastRowToCopy
    For i = 9 To lastRowToCopy
        cellValueA = Trim(CStr(wsNew.Cells(i, 1).Value)) ' العمود A
        cellValueI = Trim(CStr(wsNew.Cells(i, 9).Value)) ' العمود I
        
        ' محاولة تحويل النصوص إلى تواريخ
        If ConvertTextToDate(cellValueA, dateA) And ConvertTextToDate(cellValueI, dateI) Then
            ' حساب الفرق بالأيام
            dateDifference = dateI - dateA
            wsNew.Cells(i, 16).Value = dateDifference ' العمود P
            
            ' تنسيق الخلية حسب النتيجة
            If dateDifference > 0 Then
                wsNew.Cells(i, 16).Interior.Color = RGB(144, 238, 144) ' أخضر فاتح
            ElseIf dateDifference < 0 Then
                wsNew.Cells(i, 16).Interior.Color = RGB(255, 182, 193) ' أحمر فاتح
            Else
                wsNew.Cells(i, 16).Interior.Color = RGB(255, 255, 224) ' أصفر فاتح
            End If
        Else
            ' في حالة عدم القدرة على تحويل التاريخ
            wsNew.Cells(i, 16).Value = "خطأ في التاريخ"
            wsNew.Cells(i, 16).Interior.Color = RGB(255, 0, 0) ' أحمر
            wsNew.Cells(i, 16).Font.Color = RGB(255, 255, 255) ' نص أبيض
        End If
    Next i
    
    ' تنسيق الشيت
    FormatProcessedSheet wsNew, lastRowToCopy
    
    ' رسالة النجاح
    MsgBox "تم إنشاء الشيت الجديد بنجاح!" & vbCrLf & _
           "اسم الشيت: " & wsNew.Name & vbCrLf & _
           "تم نسخ البيانات من A1 إلى O" & lastRowToCopy & vbCrLf & _
           "تم حساب الفرق بين التواريخ في العمود P" & vbCrLf & _
           "الصفوف المعالجة: من 9 إلى " & lastRowToCopy, vbInformation
    
    ' تفعيل الشيت الجديد
    wsNew.Activate
    wsNew.Range("A1").Select
    
End Sub

' دالة لتحويل النص إلى تاريخ
Function ConvertTextToDate(textValue As String, ByRef resultDate As Date) As Boolean
    Dim cleanText As String
    Dim dateParts As Variant
    Dim day As Integer, month As Integer, year As Integer
    
    ConvertTextToDate = False
    
    ' تنظيف النص
    cleanText = Trim(textValue)
    If cleanText = "" Then Exit Function
    
    ' محاولة التحويل المباشر
    On Error Resume Next
    resultDate = CDate(cleanText)
    If Err.Number = 0 Then
        ConvertTextToDate = True
        On Error GoTo 0
        Exit Function
    End If
    On Error GoTo 0
    
    ' محاولة تحليل التاريخ يدوياً
    ' التعامل مع صيغ مختلفة: dd/mm/yyyy, dd-mm-yyyy, dd.mm.yyyy
    cleanText = Replace(cleanText, "-", "/")
    cleanText = Replace(cleanText, ".", "/")
    
    If InStr(cleanText, "/") > 0 Then
        dateParts = Split(cleanText, "/")
        If UBound(dateParts) = 2 Then
            On Error Resume Next
            day = CInt(dateParts(0))
            month = CInt(dateParts(1))
            year = CInt(dateParts(2))
            
            ' تصحيح السنة إذا كانت بصيغة مختصرة
            If year < 100 Then
                If year < 50 Then
                    year = year + 2000
                Else
                    year = year + 1900
                End If
            End If
            
            If Err.Number = 0 And day > 0 And day <= 31 And month > 0 And month <= 12 And year > 1900 Then
                resultDate = DateSerial(year, month, day)
                ConvertTextToDate = True
            End If
            On Error GoTo 0
        End If
    End If
End Function

' دالة لتنسيق الشيت المعالج
Sub FormatProcessedSheet(ws As Worksheet, lastRow As Long)
    ' تنسيق عام
    With ws.Range("A1:P" & lastRow)
        .Font.Name = "Calibri"
        .Font.Size = 10
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
        .Borders.Color = RGB(200, 200, 200)
    End With
    
    ' تنسيق الصف الأول
    With ws.Range("A1:P1")
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196)
        .Font.Color = RGB(255, 255, 255)
        .HorizontalAlignment = xlCenter
    End With
    
    ' تنسيق عمود الفرق بين التواريخ
    With ws.Range("P9:P" & lastRow)
        .HorizontalAlignment = xlCenter
        .Font.Bold = True
    End With
    
    ' ضبط عرض الأعمدة
    ws.Columns("A:P").AutoFit
    
    ' إضافة تصفية
    If lastRow > 1 Then
        ws.Range("A1:P" & lastRow).AutoFilter
    End If
    
    ' تجميد الصف الأول
    ws.Range("A2").Select
    ActiveWindow.FreezePanes = True
End Sub

' دالة مبسطة للاستخدام السريع
Sub QuickDateDifferenceCalculator()
    Dim ws As Worksheet
    Dim ordersRow As Long, lastRow As Long
    Dim foundCell As Range
    Dim i As Long
    
    Set ws = ActiveSheet
    
    ' البحث عن Orders
    Set foundCell = ws.Range("A:O").Find("Orders", , xlValues, xlPart)
    If foundCell Is Nothing Then
        MsgBox "Orders غير موجود"
        Exit Sub
    End If
    
    ordersRow = foundCell.Row
    lastRow = ordersRow - 1
    
    ' إضافة عمود للنتائج
    ws.Range("P8").Value = "Days Difference"
    ws.Range("P8").Font.Bold = True
    
    ' حساب الفرق
    For i = 9 To lastRow
        Dim dateA As Date, dateI As Date
        Dim textA As String, textI As String
        
        textA = CStr(ws.Cells(i, 1).Value)
        textI = CStr(ws.Cells(i, 9).Value)
        
        If ConvertTextToDate(textA, dateA) And ConvertTextToDate(textI, dateI) Then
            ws.Cells(i, 16).Value = dateI - dateA
        Else
            ws.Cells(i, 16).Value = "خطأ"
        End If
    Next i
    
    MsgBox "تم حساب الفرق بين التواريخ في العمود P"
End Sub
