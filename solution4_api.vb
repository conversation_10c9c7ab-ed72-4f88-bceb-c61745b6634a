' استخدام Windows API لضمان عرض النص العربي
Private Declare PtrSafe Function MessageBoxW Lib "user32" ( _
    ByVal hWnd As LongPtr, _
    ByVal lpText As LongPtr, _
    ByVal lpCaption As LongPtr, _
    ByVal wType As Long) As Long

' دالة لعرض رسالة عربية باستخدام API
Private Sub ShowArabicMessage(msg As String, Optional title As String = "", Optional msgType As Long = 0)
    If title = "" Then title = "تنبيه"
    MessageBoxW 0, StrPtr(msg), StrPtr(title), msgType
End Sub

' دالة لإنشاء النصوص العربية
Private Function CreateArabicText(textType As String) As String
    Select Case textType
        Case "select_rank"
            CreateArabicText = ChrW(1610) & ChrW(1585) & ChrW(1580) & ChrW(1609) & " " & Chr<PERSON>(1575) & Chr<PERSON>(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1605) & ChrW(1585) & ChrW(1578) & ChrW(1576) & ChrW(1577) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1610) & ChrW(1604)
        Case "enter_volume"
            CreateArabicText = ChrW(1610) & ChrW(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1573) & ChrW(1583) & ChrW(1582) & ChrW(1575) & ChrW(1604) & " " & ChrW(1571) & ChrW(1581) & ChrW(1580) & ChrW(1575) & ChrW(1605) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1583) & ChrW(1575) & ChrW(1608) & ChrW(1604) & " " & ChrW(1578) & ChrW(1581) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1575) & ChrW(1604) & ChrW(1577)
        Case "no_salary"
            CreateArabicText = ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1610) & ChrW(1604) & " " & ChrW(1604) & ChrW(1575) & " " & ChrW(1610) & ChrW(1587) & ChrW(1578) & ChrW(1581) & ChrW(1602) & " " & ChrW(1585) & ChrW(1575) & ChrW(1578) & ChrW(1576) & " " & ChrW(1604) & ChrW(1593) & ChrW(1583) & ChrW(1605) & " " & ChrW(1578) & ChrW(1581) & ChrW(1602) & ChrW(1602) & " " & ChrW(1588) & ChrW(1585) & ChrW(1591) & " " & ChrW(1578) & ChrW(1583) & ChrW(1575) & ChrW(1608) & ChrW(1604) & " 100 " & ChrW(1604) & ChrW(1608) & ChrW(1578) & " " & ChrW(1578) & ChrW(1581) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1575) & ChrW(1604) & ChrW(1577)
        Case "error_title"
            CreateArabicText = ChrW(1582) & ChrW(1591) & ChrW(1571)
        Case "warning_title"
            CreateArabicText = ChrW(1578) & ChrW(1606) & ChrW(1576) & ChrW(1610) & ChrW(1607)
    End Select
End Function

Private Sub CommandButton1_Click()
    Dim salary As Double
    Dim totalResult As Double
    
    If Not IsNumeric(Replace(Me.TextBox10.Value, "$", "")) Then
        ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48 ' vbExclamation
        Exit Sub
    End If
    
    If Trim(Me.TextBox11.Value) = "" Or Not IsNumeric(Replace(Me.TextBox11.Value, " Lot", "")) Then
        ShowArabicMessage CreateArabicText("enter_volume"), CreateArabicText("warning_title"), 48 ' vbExclamation
        Me.TextBox9.Value = ""
        Exit Sub
    End If
    
    If CDbl(Replace(Me.TextBox11.Value, " Lot", "")) < 100 Then
        ShowArabicMessage CreateArabicText("no_salary"), CreateArabicText("error_title"), 16 ' vbCritical
        Me.TextBox9.Value = ""
        Exit Sub
    End If
    
    salary = CDbl(Replace(Me.TextBox10.Value, "$", ""))
    totalResult = 0
    
    ' باقي الكود للحسابات...
    If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1610) & ChrW(1608) & ChrW(1605) & ChrW(1610) & ": 30 / 30 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1576) & ChrW(1608) & ChrW(1587) & ChrW(1578) & ChrW(1575) & ChrW(1578) & " " & ChrW(1573) & ChrW(1579) & ChrW(1576) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1587) & ChrW(1581) & ChrW(1576) & ": 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1605) & ChrW(1606) & ChrW(1588) & ChrW(1608) & ChrW(1585) & ChrW(1575) & ChrW(1578) & " " & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1575) & ChrW(1576) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1573) & ChrW(1606) & ChrW(1586) & ChrW(1608) & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1606) & ChrW(1583) & ChrW(1608) & ChrW(1577) & " " & ChrW(1604) & ChrW(1575) & ChrW(1610) & ChrW(1601) & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.1)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1585) & ChrW(1610) & ChrW(1604) & ChrW(1586) & ChrW(1585) & ChrW(1575) & ChrW(1578) & " " & ChrW(1578) & ChrW(1601) & ChrW(1575) & ChrW(1593) & ChrW(1604) & ChrW(1610) & ChrW(1577) & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.08)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1583) & ChrW(1608) & ChrW(1585) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1593) & ChrW(1604) & ChrW(1610) & ChrW(1605) & ChrW(1610) & ChrW(1577) & ": 5 / 5 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577) & ": 4 / 4 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1573) & ChrW(1579) & ChrW(1576) & ChrW(1575) & ChrW(1578) & " " & ChrW(1587) & ChrW(1581) & ChrW(1576) & " - " & ChrW(1576) & ChrW(1608) & ChrW(1606) & ChrW(1589) & " " & ChrW(1578) & ChrW(1585) & ChrW(1581) & ChrW(1610) & ChrW(1576) & ChrW(1610) & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1578) & ChrW(1593) & ChrW(1575) & ChrW(1608) & ChrW(1606) & " " & ChrW(1605) & ChrW(1593) & " " & ChrW(1588) & ChrW(1585) & ChrW(1603) & ChrW(1577) & ": 1 / 1 ") > 0 Then totalResult = totalResult + (salary * 0.1)
    
    Me.TextBox9.Value = Format(totalResult, "$#,##0.00")
End Sub

' دالة اختبار لفحص دعم Unicode
Private Sub TestArabicDisplay()
    Dim testMsg As String
    testMsg = ChrW(1575) & ChrW(1604) & ChrW(1587) & ChrW(1604) & ChrW(1575) & ChrW(1605) & " " & ChrW(1593) & ChrW(1604) & ChrW(1610) & ChrW(1603) & ChrW(1605)
    ShowArabicMessage testMsg, "اختبار", 64
End Sub
