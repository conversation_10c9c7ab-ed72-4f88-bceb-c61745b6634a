Private Sub CommandButton3_Click()
    Dim inputValue As Double
    Dim resultValue As Double
    Dim multiplier As Long
    Dim val As String
    val = Replace(Me.TextBox17.Value, " Lot", "")

    If IsNumeric(val) Then
        inputValue = CDbl(val)
    Else
        ' من فضلك أدخل رقم صحيح في الخانة.
        MsgBox ChrW(1605) & ChrW(1606) & " " & ChrW(1601) & ChrW(1590) & ChrW(1604) & ChrW(1603) & " " & ChrW(1571) & ChrW(1583) & ChrW(1582) & ChrW(1604) & " " & ChrW(1585) & ChrW(1602) & ChrW(1605) & " " & ChrW(1589) & ChrW(1581) & ChrW(1610) & ChrW(1581) & " " & ChrW(1601) & ChrW(1610) & " " & ChrW(1575) & ChrW(1604) & Chr<PERSON>(1582) & ChrW(1575) & ChrW(1606) & ChrW(1577) & ".", vbExclamation
        Exit Sub
    End If
    
    Select Case Me.ComboBox5.Value
        Case "Pro"
            multiplier = Int(inputValue / 200)
            resultValue = multiplier * 100
        Case "Elite"
            multiplier = Int(inputValue / 300)
            resultValue = multiplier * 150
        Case "Bronze"
            If inputValue > 200 Then
                resultValue = (inputValue - 200) * 0.25
            Else
                resultValue = 0
            End If
        Case "Silver"
            If inputValue > 300 Then
                resultValue = (inputValue - 300) * 0.25
            Else
                resultValue = 0
            End If
        Case "Gold"
            If inputValue > 600 Then
                resultValue = (inputValue - 600) * 0.25
            Else
                resultValue = 0
            End If
        Case "Platinum"
            If inputValue > 1200 Then
                resultValue = (inputValue - 1200) * 0.25
            Else
                resultValue = 0
            End If
        Case "Diamond"
            If inputValue > 2500 Then
                resultValue = (inputValue - 2500) * 0.25
            Else
                resultValue = 0
            End If
        Case "Sapphire"
            If inputValue > 5000 Then
                resultValue = (inputValue - 5000) * 0.25
            Else
                resultValue = 0
            End If
        Case "Emerald"
            If inputValue > 10000 Then
                resultValue = (inputValue - 10000) * 0.25
            Else
                resultValue = 0
            End If
        Case "King"
            If inputValue > 20000 Then
                resultValue = (inputValue - 20000) * 0.25
            Else
                resultValue = 0
            End If
        Case "The Legend"
            If inputValue > 40000 Then
                resultValue = (inputValue - 40000) * 0.25
            Else
                resultValue = 0
            End If
        Case Else
            ' يرجى اختيار مرتبة الوكيل
            MsgBox ChrW(1610) & ChrW(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1605) & ChrW(1585) & ChrW(1578) & ChrW(1576) & ChrW(1577) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1610) & ChrW(1604), vbExclamation
            Exit Sub
    End Select
    
    Me.TextBox15.Value = Format(resultValue, "$#,##0.00")
End Sub

' ملاحظات حول التحويل:
' 
' النص الأول: "من فضلك أدخل رقم صحيح في الخانة."
' تم تحويله إلى:
' ChrW(1605) & ChrW(1606) & " " & ChrW(1601) & ChrW(1590) & ChrW(1604) & ChrW(1603) & " " & ChrW(1571) & ChrW(1583) & ChrW(1582) & ChrW(1604) & " " & ChrW(1585) & ChrW(1602) & ChrW(1605) & " " & ChrW(1589) & ChrW(1581) & ChrW(1610) & ChrW(1581) & " " & ChrW(1601) & ChrW(1610) & " " & ChrW(1575) & ChrW(1604) & ChrW(1582) & ChrW(1575) & ChrW(1606) & ChrW(1577) & "."
'
' النص الثاني: "يرجى اختيار مرتبة الوكيل"
' تم تحويله إلى:
' ChrW(1610) & ChrW(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1605) & ChrW(1585) & ChrW(1578) & ChrW(1576) & ChrW(1577) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1610) & ChrW(1604)
'
' رموز Unicode المستخدمة:
' م = ChrW(1605)  |  ن = ChrW(1606)  |  ف = ChrW(1601)  |  ض = ChrW(1590)
' ل = ChrW(1604)  |  ك = ChrW(1603)  |  أ = ChrW(1571)  |  د = ChrW(1583)
' خ = ChrW(1582)  |  ر = ChrW(1585)  |  ق = ChrW(1602)  |  ص = ChrW(1589)
' ح = ChrW(1581)  |  ي = ChrW(1610)  |  ا = ChrW(1575)  |  ة = ChrW(1577)
' ج = ChrW(1580)  |  ى = ChrW(1609)  |  ت = ChrW(1578)  |  ب = ChrW(1576)
' و = ChrW(1608)
