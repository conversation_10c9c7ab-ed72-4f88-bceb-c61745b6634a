Sub FinalAdvancedDataConverter()
    Dim wsSource As Worksheet
    Dim wsNew As Worksheet
    Dim ordersRow As Long
    Dim lastRowToCopy As Long
    Dim response As VbMsgBoxResult
    
    ' التحقق من وجود Sheet1
    On Error Resume Next
    Set wsSource = ThisWorkbook.Sheets("Sheet1")
    On Error GoTo 0
    
    If wsSource Is Nothing Then
        MsgBox "لم يتم العثور على Sheet1", vbCritical
        Exit Sub
    End If
    
    ' البحث عن كلمة "Orders"
    ordersRow = FindOrdersRow(wsSource)
    
    If ordersRow = 0 Then
        MsgBox "لم يتم العثور على كلمة 'Orders' في الشيت", vbExclamation
        Exit Sub
    End If
    
    ' تحديد آخر صف للنسخ (الصف الذي يسبق Orders)
    lastRowToCopy = ordersRow - 1
    
    If lastRowToCopy < 1 Then
        MsgBox "لا توجد بيانات للنسخ قبل صف Orders", vbExclamation
        Exit Sub
    End If
    
    ' تأكيد من المستخدم
    response = MsgBox("تم العثور على كلمة 'Orders' في الصف " & ordersRow & vbCrLf & _
                     "سيتم نسخ البيانات من الصف 1 إلى الصف " & lastRowToCopy & vbCrLf & _
                     "الخلايا A1:A7 ستحتفظ بتنسيقها الأصلي" & vbCrLf & _
                     "هل تريد المتابعة؟", vbYesNo + vbQuestion)
    
    If response = vbNo Then Exit Sub
    
    ' إنشاء الشيت الجديد
    Set wsNew = CreateNewSheet()
    
    ' نسخ البيانات مع الحفاظ على تنسيق A1:A7
    CopyDataWithOriginalFormatting wsSource, wsNew, lastRowToCopy
    
    ' حذف العمودين M و N
    DeleteColumnsM_N wsNew
    
    ' إضافة كلمة "Profit" في الخلية M8
    wsNew.Range("M8").Value = "Profit"
    
    ' تنسيق الشيت الجديد (مع تجنب A1:A7)
    FormatSheetPreservingA1A7 wsNew, lastRowToCopy
    
    ' رسالة النجاح
    MsgBox "تم إنشاء الشيت الجديد بنجاح!" & vbCrLf & _
           "اسم الشيت: " & wsNew.Name & vbCrLf & _
           "البيانات المنسوخة: A1:O" & lastRowToCopy & vbCrLf & _
           "الخلايا A1:A7 تم نسخها بتنسيقها الأصلي" & vbCrLf & _
           "تم حذف العمودين M و N" & vbCrLf & _
           "تم إضافة 'Profit' في الخلية M8", vbInformation
    
    ' تفعيل الشيت الجديد
    wsNew.Activate
    wsNew.Range("A1").Select
    
End Sub

' دالة للبحث عن صف "Orders"
Function FindOrdersRow(ws As Worksheet) As Long
    Dim i As Long, j As Long
    Dim cellValue As String
    
    FindOrdersRow = 0
    
    For i = 1 To 1000
        For j = 1 To 15 ' من العمود A إلى O
            cellValue = Trim(CStr(ws.Cells(i, j).Value))
            If InStr(1, cellValue, "Orders", vbTextCompare) > 0 Then
                FindOrdersRow = i
                Exit Function
            End If
        Next j
    Next i
End Function

' دالة لإنشاء شيت جديد
Function CreateNewSheet() As Worksheet
    Dim newSheetName As String
    Dim counter As Integer
    
    counter = 1
    newSheetName = "ProcessedData"
    
    Do While SheetExists(newSheetName)
        newSheetName = "ProcessedData_" & counter
        counter = counter + 1
    Loop
    
    Set CreateNewSheet = ThisWorkbook.Sheets.Add
    CreateNewSheet.Name = newSheetName
End Function

' دالة للتحقق من وجود شيت
Function SheetExists(sheetName As String) As Boolean
    Dim ws As Worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets(sheetName)
    SheetExists = Not ws Is Nothing
    On Error GoTo 0
End Function

' دالة لنسخ البيانات مع الحفاظ على تنسيق A1:A7
Sub CopyDataWithOriginalFormatting(wsSource As Worksheet, wsTarget As Worksheet, lastRow As Long)
    Dim i As Long, j As Long
    Dim cellValue As Variant
    
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    ' نسخ الخلايا A1:A7 بتنسيقها الأصلي كاملاً
    If lastRow >= 7 Then
        wsSource.Range("A1:A7").Copy
        wsTarget.Range("A1:A7").PasteSpecial xlPasteAll
        Application.CutCopyMode = False
    ElseIf lastRow > 0 Then
        ' إذا كان عدد الصفوف أقل من 7
        wsSource.Range("A1:A" & lastRow).Copy
        wsTarget.Range("A1:A" & lastRow).PasteSpecial xlPasteAll
        Application.CutCopyMode = False
    End If
    
    ' نسخ باقي البيانات مع التحويل
    For i = 1 To lastRow
        For j = 1 To 15 ' من العمود A إلى O
            ' تخطي العمود A للصفوف من 1 إلى 7 (تم نسخها بالفعل)
            If j = 1 And i <= 7 Then
                GoTo NextColumn
            End If
            
            cellValue = wsSource.Cells(i, j).Value
            
            ' تحويل النص إلى رقم إذا أمكن
            If IsNumeric(cellValue) And cellValue <> "" Then
                wsTarget.Cells(i, j).Value = CDbl(cellValue)
            ElseIf IsDate(cellValue) Then
                wsTarget.Cells(i, j).Value = CDate(cellValue)
            Else
                wsTarget.Cells(i, j).Value = cellValue
            End If
            
NextColumn:
        Next j
    Next i
    
    Application.Calculation = xlCalculationAutomatic
    Application.ScreenUpdating = True
End Sub

' دالة لحذف العمودين M و N
Sub DeleteColumnsM_N(ws As Worksheet)
    Application.DisplayAlerts = False
    ws.Columns(14).Delete ' حذف العمود N (العمود 14)
    ws.Columns(13).Delete ' حذف العمود M (العمود 13)
    Application.DisplayAlerts = True
End Sub

' دالة لتنسيق الشيت مع الحفاظ على A1:A7
Sub FormatSheetPreservingA1A7(ws As Worksheet, lastRow As Long)
    Dim dataRange As Range
    
    ' تنسيق البيانات من B1 إلى M7 (تجنب العمود A)
    If lastRow >= 7 Then
        Set dataRange = ws.Range("B1:M7")
        With dataRange
            .Font.Name = "Calibri"
            .Font.Size = 11
            .Borders.LineStyle = xlContinuous
            .Borders.Weight = xlThin
            .Borders.Color = RGB(200, 200, 200)
        End With
        
        ' تنسيق العناوين في الصف الأول (تجنب A1)
        With ws.Range("B1:M1")
            .Font.Bold = True
            .Font.Color = RGB(255, 255, 255)
            .Interior.Color = RGB(68, 114, 196)
            .HorizontalAlignment = xlCenter
        End With
    End If
    
    ' تنسيق البيانات من A8 إلى M(lastRow) إذا كان هناك صفوف بعد الصف 7
    If lastRow > 7 Then
        Set dataRange = ws.Range("A8:M" & lastRow)
        With dataRange
            .Font.Name = "Calibri"
            .Font.Size = 11
            .Borders.LineStyle = xlContinuous
            .Borders.Weight = xlThin
            .Borders.Color = RGB(200, 200, 200)
        End With
    End If
    
    ' تنسيق خاص لخلية "Profit"
    With ws.Range("M8")
        .Font.Bold = True
        .Font.Size = 12
        .Font.Color = RGB(0, 128, 0)
        .Interior.Color = RGB(220, 255, 220)
        .HorizontalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlMedium
        .Borders.Color = RGB(0, 128, 0)
    End With
    
    ' ضبط عرض الأعمدة (تجنب العمود A)
    ws.Columns("B:M").AutoFit
    
    ' إضافة تصفية تلقائية
    If lastRow > 1 Then
        ws.Range("A1:M" & lastRow).AutoFilter
    End If
    
    ' تجميد الصف الأول
    ws.Range("A2").Select
    ActiveWindow.FreezePanes = True
End Sub

' دالة سريعة للتشغيل
Sub RunFinalConverter()
    Call FinalAdvancedDataConverter
End Sub
