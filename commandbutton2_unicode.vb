Private Sub CommandButton2_Click()
    Dim txt As String
    Dim val5 As Double, val6 As Double
    Dim totalValue As Double
    Dim selectedRank As String
    txt = Me.TextBox7.Value
    totalValue = 0
    selectedRank = Me.ComboBox1.Value
    
    If IsNumeric(Replace(Me.TextBox12.Value, "$", "")) Then
        val5 = CDbl(Replace(Me.TextBox12.Value, "$", ""))
    Else
        val5 = 0
    End If
    
    If IsNumeric(Replace(Me.TextBox13.Value, "$", "")) Then
        val6 = CDbl(Replace(Me.TextBox13.Value, "$", ""))
    Else
        val6 = 0
    End If
    
    ' عرض نشر يومي: 30 / 30
    If InStr(txt, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1610) & ChrW(1608) & ChrW(1605) & ChrW(1610) & ": 30 / 30") > 0 Then
        totalValue = val5
    End If
    
    If Not (selectedRank = "Beginning" Or selectedRank = "Growth") Then
        ' عرض التحليلات الأسبوعية: 1 / 4
        If InStr(txt, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577) & ": 1 / 4") > 0 Then totalValue = totalValue + val6
        
        ' عرض التحليلات الأسبوعية: 2 / 4
        If InStr(txt, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577) & ": 2 / 4") > 0 Then totalValue = totalValue + (2 * val6)
        
        ' عرض التحليلات الأسبوعية: 3 / 4
        If InStr(txt, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577) & ": 3 / 4") > 0 Then totalValue = totalValue + (3 * val6)
        
        ' عرض التحليلات الأسبوعية: 4 / 4
        If InStr(txt, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577) & ": 4 / 4") > 0 Then totalValue = totalValue + (4 * val6)
    End If
    
    Me.TextBox8.Value = Format(totalValue, "$#,##0.00")
End Sub

' ملاحظات حول التحويل:
' النص "عرض نشر يومي" تم تحويله إلى:
' ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1610) & ChrW(1608) & ChrW(1605) & ChrW(1610)
'
' النص "عرض التحليلات الأسبوعية" تم تحويله إلى:
' ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577)
'
' رموز Unicode المستخدمة:
' ع = ChrW(1593)  |  ر = ChrW(1585)  |  ض = ChrW(1590)  |  ن = ChrW(1606)
' ش = ChrW(1588)  |  ي = ChrW(1610)  |  و = ChrW(1608)  |  م = ChrW(1605)
' ا = ChrW(1575)  |  ل = ChrW(1604)  |  ت = ChrW(1578)  |  ح = ChrW(1581)
' أ = ChrW(1571)  |  س = ChrW(1587)  |  ب = ChrW(1576)  |  ة = ChrW(1577)
