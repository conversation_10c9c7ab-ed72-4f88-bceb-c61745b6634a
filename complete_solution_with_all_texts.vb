' ===== الجزء الأول: يوضع في Module =====
Private Declare PtrSafe Function MessageBoxW Lib "user32" ( _
    ByVal hWnd As LongPtr, _
    ByVal lpText As LongPtr, _
    ByVal lpCaption As LongPtr, _
    ByVal wType As Long) As Long

Public Sub ShowArabicMessage(msg As String, Optional title As String = "", Optional msgType As Long = 0)
    If title = "" Then title = ChrW(1578) & ChrW(1606) & ChrW(1576) & ChrW(1610) & ChrW(1607) ' تنبيه
    MessageBoxW 0, StrPtr(msg), StrPtr(title), msgType
End Sub

Public Function CreateArabicText(textType As String) As String
    Select Case textType
        ' رسائل الأخطاء والتحذيرات
        Case "select_rank"
            ' يرجى اختيار مرتبة الوكيل
            CreateArabicText = ChrW(1610) & Chr<PERSON>(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1605) & ChrW(1585) & ChrW(1578) & ChrW(1576) & ChrW(1577) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1610) & ChrW(1604)
        Case "enter_volume"
            ' يرجى إدخال أحجام التداول تحت الوكالة
            CreateArabicText = ChrW(1610) & ChrW(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1573) & ChrW(1583) & ChrW(1582) & ChrW(1575) & ChrW(1604) & " " & ChrW(1571) & ChrW(1581) & ChrW(1580) & ChrW(1575) & ChrW(1605) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1583) & ChrW(1575) & ChrW(1608) & ChrW(1604) & " " & ChrW(1578) & ChrW(1581) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1575) & ChrW(1604) & ChrW(1577)
        Case "no_salary"
            ' الوكيل لا يستحق راتب لعدم تحقق شرط تداول 100 لوت تحت الوكالة
            CreateArabicText = ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1610) & ChrW(1604) & " " & ChrW(1604) & ChrW(1575) & " " & ChrW(1610) & ChrW(1587) & ChrW(1578) & ChrW(1581) & ChrW(1602) & " " & ChrW(1585) & ChrW(1575) & ChrW(1578) & ChrW(1576) & " " & ChrW(1604) & ChrW(1593) & ChrW(1583) & ChrW(1605) & " " & ChrW(1578) & ChrW(1581) & ChrW(1602) & ChrW(1602) & " " & ChrW(1588) & ChrW(1585) & ChrW(1591) & " " & ChrW(1578) & ChrW(1583) & ChrW(1575) & ChrW(1608) & ChrW(1604) & " 100 " & ChrW(1604) & ChrW(1608) & ChrW(1578) & " " & ChrW(1578) & ChrW(1581) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1575) & ChrW(1604) & ChrW(1577)
        Case "select_daily_level"
            ' يرجى اختيار مستوى عرض النشر اليومي.
            CreateArabicText = ChrW(1610) & ChrW(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1605) & ChrW(1587) & ChrW(1578) & ChrW(1608) & ChrW(1609) & " " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1575) & ChrW(1604) & ChrW(1610) & ChrW(1608) & ChrW(1605) & ChrW(1610) & "."
        Case "enter_correct_number"
            ' من فضلك أدخل رقم صحيح في الخانة.
            CreateArabicText = ChrW(1605) & ChrW(1606) & " " & ChrW(1601) & ChrW(1590) & ChrW(1604) & ChrW(1603) & " " & ChrW(1571) & ChrW(1583) & ChrW(1582) & ChrW(1604) & " " & ChrW(1585) & ChrW(1602) & ChrW(1605) & " " & ChrW(1589) & ChrW(1581) & ChrW(1610) & ChrW(1581) & " " & ChrW(1601) & ChrW(1610) & " " & ChrW(1575) & ChrW(1604) & ChrW(1582) & ChrW(1575) & ChrW(1606) & ChrW(1577) & "."
        
        ' عناوين الرسائل
        Case "error_title"
            ' خطأ
            CreateArabicText = ChrW(1582) & ChrW(1591) & ChrW(1571)
        Case "warning_title"
            ' تنبيه
            CreateArabicText = ChrW(1578) & ChrW(1606) & ChrW(1576) & ChrW(1610) & ChrW(1607)
        
        ' النصوص المستخدمة في البحث
        Case "daily_post"
            ' عرض نشر يومي
            CreateArabicText = ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1610) & ChrW(1608) & ChrW(1605) & ChrW(1610)
        Case "withdrawal_proof"
            ' عرض بوستات إثبات السحب
            CreateArabicText = ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1576) & ChrW(1608) & ChrW(1587) & ChrW(1578) & ChrW(1575) & ChrW(1578) & " " & ChrW(1573) & ChrW(1579) & ChrW(1576) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1587) & ChrW(1581) & ChrW(1576)
        Case "enzo_reasons"
            ' منشورات أسباب اختيار إنزو
            CreateArabicText = ChrW(1605) & ChrW(1606) & ChrW(1588) & ChrW(1608) & ChrW(1585) & ChrW(1575) & ChrW(1578) & " " & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1575) & ChrW(1576) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1573) & ChrW(1606) & ChrW(1586) & ChrW(1608)
        Case "live_webinar"
            ' ندوة لايف
            CreateArabicText = ChrW(1606) & ChrW(1583) & ChrW(1608) & ChrW(1577) & " " & ChrW(1604) & ChrW(1575) & ChrW(1610) & ChrW(1601)
        Case "interactive_reels"
            ' عرض ريلزرات تفاعلية
            CreateArabicText = ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1585) & ChrW(1610) & ChrW(1604) & ChrW(1586) & ChrW(1585) & ChrW(1575) & ChrW(1578) & " " & ChrW(1578) & ChrW(1601) & ChrW(1575) & ChrW(1593) & ChrW(1604) & ChrW(1610) & ChrW(1577)
        Case "educational_courses"
            ' عرض الدورات التعليمية
            CreateArabicText = ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1583) & ChrW(1608) & ChrW(1585) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1593) & ChrW(1604) & ChrW(1610) & ChrW(1605) & ChrW(1610) & ChrW(1577)
        Case "weekly_analysis"
            ' عرض التحليلات الأسبوعية
            CreateArabicText = ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577)
        Case "withdrawal_bonus"
            ' نشر إثبات سحب - بونص ترحيبي
            CreateArabicText = ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1573) & ChrW(1579) & ChrW(1576) & ChrW(1575) & ChrW(1578) & " " & ChrW(1587) & ChrW(1581) & ChrW(1576) & " - " & ChrW(1576) & ChrW(1608) & ChrW(1606) & ChrW(1589) & " " & ChrW(1578) & ChrW(1585) & ChrW(1581) & ChrW(1610) & ChrW(1576) & ChrW(1610)
        Case "company_cooperation"
            ' تعاون مع شركة
            CreateArabicText = ChrW(1578) & ChrW(1593) & ChrW(1575) & ChrW(1608) & ChrW(1606) & " " & ChrW(1605) & ChrW(1593) & " " & ChrW(1588) & ChrW(1585) & ChrW(1603) & ChrW(1577)
    End Select
End Function

' دالة اختبار لفحص دعم Unicode
Public Sub TestArabicDisplay()
    Dim testMsg As String
    testMsg = ChrW(1575) & ChrW(1604) & ChrW(1587) & ChrW(1604) & ChrW(1575) & ChrW(1605) & " " & ChrW(1593) & ChrW(1604) & ChrW(1610) & ChrW(1603) & ChrW(1605) ' السلام عليكم
    ShowArabicMessage testMsg, "اختبار", 64
End Sub

' ===== الجزء الثاني: يوضع في UserForm =====

' الزر الأول - CommandButton1
Private Sub CommandButton1_Click()
    Dim salary As Double
    Dim totalResult As Double

    If Not IsNumeric(Replace(Me.TextBox10.Value, "$", "")) Then
        ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48
        Exit Sub
    End If

    If Trim(Me.TextBox11.Value) = "" Or Not IsNumeric(Replace(Me.TextBox11.Value, " Lot", "")) Then
        ShowArabicMessage CreateArabicText("enter_volume"), CreateArabicText("warning_title"), 48
        Me.TextBox9.Value = ""
        Exit Sub
    End If

    If CDbl(Replace(Me.TextBox11.Value, " Lot", "")) < 100 Then
        ShowArabicMessage CreateArabicText("no_salary"), CreateArabicText("error_title"), 16
        Me.TextBox9.Value = ""
        Exit Sub
    End If

    salary = CDbl(Replace(Me.TextBox10.Value, "$", ""))
    totalResult = 0

    ' الحسابات باستخدام النصوص العربية
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("daily_post") & ": 30 / 30 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("withdrawal_proof") & ": 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("enzo_reasons") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("live_webinar") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.1)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("interactive_reels") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.08)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("educational_courses") & ": 5 / 5 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("weekly_analysis") & ": 4 / 4 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("withdrawal_bonus") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("company_cooperation") & ": 1 / 1 ") > 0 Then totalResult = totalResult + (salary * 0.1)

    Me.TextBox9.Value = Format(totalResult, "$#,##0.00")
End Sub

' الزر الثاني - CommandButton2
Private Sub CommandButton2_Click()
    Dim txt As String
    Dim val5 As Double, val6 As Double
    Dim totalValue As Double
    Dim selectedRank As String

    txt = Me.TextBox7.Value
    totalValue = 0
    selectedRank = Me.ComboBox1.Value

    If Trim(Me.TextBox12.Value) = "" Or Not IsNumeric(Replace(Me.TextBox12.Value, "$", "")) Then
        ShowArabicMessage CreateArabicText("select_daily_level"), CreateArabicText("error_title"), 16
        Me.TextBox12.SetFocus
        Exit Sub
    End If

    val5 = CDbl(Replace(Me.TextBox12.Value, "$", ""))

    If IsNumeric(Replace(Me.TextBox13.Value, "$", "")) Then
        val6 = CDbl(Replace(Me.TextBox13.Value, "$", ""))
    Else
        val6 = 0
    End If

    If InStr(txt, "- " & CreateArabicText("daily_post") & ": 30 / 30") > 0 Then
        totalValue = val5
    End If

    If Not (selectedRank = "Beginning" Or selectedRank = "Growth") Then
        If InStr(txt, "- " & CreateArabicText("weekly_analysis") & ": 1 / 4") > 0 Then totalValue = totalValue + val6
        If InStr(txt, "- " & CreateArabicText("weekly_analysis") & ": 2 / 4") > 0 Then totalValue = totalValue + (2 * val6)
        If InStr(txt, "- " & CreateArabicText("weekly_analysis") & ": 3 / 4") > 0 Then totalValue = totalValue + (3 * val6)
        If InStr(txt, "- " & CreateArabicText("weekly_analysis") & ": 4 / 4") > 0 Then totalValue = totalValue + (4 * val6)
    End If

    Me.TextBox8.Value = Format(totalValue, "$#,##0.00")
End Sub

' الزر الثالث - CommandButton3
Private Sub CommandButton3_Click()
    Dim inputValue As Double
    Dim resultValue As Double
    Dim multiplier As Long
    Dim val As String

    val = Replace(Me.TextBox17.Value, " Lot", "")

    If IsNumeric(val) Then
        inputValue = CDbl(val)
    Else
        ShowArabicMessage CreateArabicText("enter_correct_number"), CreateArabicText("warning_title"), 48
        Exit Sub
    End If

    Select Case Me.ComboBox5.Value
        Case "Pro"
            multiplier = Int(inputValue / 200)
            resultValue = multiplier * 100
        Case "Elite"
            multiplier = Int(inputValue / 300)
            resultValue = multiplier * 150
        Case "Bronze"
            If inputValue > 200 Then
                resultValue = (inputValue - 200) * 0.25
            Else
                resultValue = 0
            End If
        Case "Silver"
            If inputValue > 300 Then
                resultValue = (inputValue - 300) * 0.25
            Else
                resultValue = 0
            End If
        Case "Gold"
            If inputValue > 600 Then
                resultValue = (inputValue - 600) * 0.25
            Else
                resultValue = 0
            End If
        Case "Platinum"
            If inputValue > 1200 Then
                resultValue = (inputValue - 1200) * 0.25
            Else
                resultValue = 0
            End If
        Case "Diamond"
            If inputValue > 2500 Then
                resultValue = (inputValue - 2500) * 0.25
            Else
                resultValue = 0
            End If
        Case "Sapphire"
            If inputValue > 5000 Then
                resultValue = (inputValue - 5000) * 0.25
            Else
                resultValue = 0
            End If
        Case "Emerald"
            If inputValue > 10000 Then
                resultValue = (inputValue - 10000) * 0.25
            Else
                resultValue = 0
            End If
        Case "King"
            If inputValue > 20000 Then
                resultValue = (inputValue - 20000) * 0.25
            Else
                resultValue = 0
            End If
        Case "The Legend"
            If inputValue > 40000 Then
                resultValue = (inputValue - 40000) * 0.25
            Else
                resultValue = 0
            End If
        Case Else
            ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48
            Exit Sub
    End Select

    Me.TextBox15.Value = Format(resultValue, "$#,##0.00")
End Sub
