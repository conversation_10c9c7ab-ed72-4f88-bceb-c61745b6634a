Sub AdvancedDateDifferenceProcessor()
    ' كود متقدم لمعالجة البيانات وحساب الفرق بين التواريخ
    
    Dim wsSource As Worksheet
    Dim wsNew As Worksheet
    Dim ordersRow As Long, lastRowToCopy As Long
    Dim foundCell As Range
    Dim i As <PERSON>, j <PERSON> Long
    Dim processedCount As Long, errorCount As Long
    
    ' تعيين الشيت المصدر
    Set wsSource = ActiveSheet
    
    ' البحث عن كلمة "Orders"
    Set foundCell = wsSource.Range("A:O").Find("Orders", , xlValues, xlPart, , , False)
    
    If foundCell Is Nothing Then
        MsgBox "لم يتم العثور على كلمة 'Orders' في النطاق A:O", vbExclamation, "خطأ"
        Exit Sub
    End If
    
    ordersRow = foundCell.Row
    lastRowToCopy = ordersRow - 1
    
    ' التحقق من وجود بيانات كافية
    If lastRowToCopy < 9 Then
        MsgBox "لا توجد بيانات كافية للمعالجة" & vbCrLf & _
               "يجب أن يكون هناك على الأقل 9 صفوف قبل كلمة Orders" & vbCrLf & _
               "Orders موجود في الصف: " & ordersRow, vbExclamation, "بيانات غير كافية"
        Exit Sub
    End If
    
    ' عرض معلومات للمستخدم
    Dim response As VbMsgBoxResult
    response = MsgBox("معلومات المعالجة:" & vbCrLf & _
                     "• موقع Orders: الصف " & ordersRow & vbCrLf & _
                     "• نطاق النسخ: A1:O" & lastRowToCopy & vbCrLf & _
                     "• نطاق حساب التواريخ: الصف 9 إلى " & lastRowToCopy & vbCrLf & _
                     "• العمود A: التاريخ الأول" & vbCrLf & _
                     "• العمود I: التاريخ الثاني" & vbCrLf & vbCrLf & _
                     "هل تريد المتابعة؟", vbYesNo + vbQuestion, "تأكيد المعالجة")
    
    If response = vbNo Then Exit Sub
    
    ' إنشاء شيت جديد
    Set wsNew = CreateProcessedSheet()
    
    ' نسخ البيانات الأساسية
    CopySourceData wsSource, wsNew, lastRowToCopy
    
    ' إضافة عناوين للأعمدة الجديدة
    SetupNewColumns wsNew
    
    ' معالجة التواريخ وحساب الفروق
    ProcessDateDifferences wsNew, lastRowToCopy, processedCount, errorCount
    
    ' تطبيق التنسيق
    ApplyAdvancedFormatting wsNew, lastRowToCopy
    
    ' عرض تقرير النتائج
    ShowProcessingReport wsNew.Name, lastRowToCopy, processedCount, errorCount, ordersRow
    
    ' تفعيل الشيت الجديد
    wsNew.Activate
    wsNew.Range("A1").Select
    
End Sub

' دالة لإنشاء شيت جديد
Function CreateProcessedSheet() As Worksheet
    Dim newSheetName As String
    Dim counter As Integer
    
    counter = 1
    newSheetName = "DateProcessed"
    
    ' التأكد من عدم وجود شيت بنفس الاسم
    Do While SheetExists(newSheetName)
        newSheetName = "DateProcessed_" & counter
        counter = counter + 1
    Loop
    
    Set CreateProcessedSheet = Sheets.Add
    CreateProcessedSheet.Name = newSheetName
End Function

' دالة للتحقق من وجود شيت
Function SheetExists(sheetName As String) As Boolean
    Dim ws As Worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets(sheetName)
    SheetExists = Not ws Is Nothing
    On Error GoTo 0
End Function

' دالة لنسخ البيانات المصدر
Sub CopySourceData(wsSource As Worksheet, wsTarget As Worksheet, lastRow As Long)
    Application.ScreenUpdating = False
    
    ' نسخ البيانات مع القيم والتنسيق
    wsSource.Range("A1:O" & lastRow).Copy
    wsTarget.Range("A1").PasteSpecial xlPasteValues
    wsTarget.Range("A1").PasteSpecial xlPasteFormats
    Application.CutCopyMode = False
    
    Application.ScreenUpdating = True
End Sub

' دالة لإعداد الأعمدة الجديدة
Sub SetupNewColumns(ws As Worksheet)
    ' إضافة عناوين للأعمدة الجديدة
    With ws.Range("P8")
        .Value = "Date A (Converted)"
        .Font.Bold = True
        .Interior.Color = RGB(173, 216, 230) ' أزرق فاتح
        .HorizontalAlignment = xlCenter
    End With
    
    With ws.Range("Q8")
        .Value = "Date I (Converted)"
        .Font.Bold = True
        .Interior.Color = RGB(173, 216, 230) ' أزرق فاتح
        .HorizontalAlignment = xlCenter
    End With
    
    With ws.Range("R8")
        .Value = "Difference (Days)"
        .Font.Bold = True
        .Interior.Color = RGB(255, 255, 0) ' أصفر
        .HorizontalAlignment = xlCenter
    End With
    
    With ws.Range("S8")
        .Value = "Status"
        .Font.Bold = True
        .Interior.Color = RGB(255, 192, 203) ' وردي فاتح
        .HorizontalAlignment = xlCenter
    End With
End Sub

' دالة لمعالجة التواريخ وحساب الفروق
Sub ProcessDateDifferences(ws As Worksheet, lastRow As Long, ByRef processedCount As Long, ByRef errorCount As Long)
    Dim i As Long
    Dim dateA As Date, dateI As Date
    Dim textA As String, textI As String
    Dim dateDiff As Long
    Dim statusMsg As String
    
    processedCount = 0
    errorCount = 0
    
    For i = 9 To lastRow
        ' قراءة القيم النصية
        textA = Trim(CStr(ws.Cells(i, 1).Value)) ' العمود A
        textI = Trim(CStr(ws.Cells(i, 9).Value)) ' العمود I
        
        ' محاولة تحويل التواريخ
        Dim convertA As Boolean, convertI As Boolean
        convertA = ConvertTextToDateAdvanced(textA, dateA)
        convertI = ConvertTextToDateAdvanced(textI, dateI)
        
        ' كتابة التواريخ المحولة
        If convertA Then
            ws.Cells(i, 16).Value = dateA ' العمود P
            ws.Cells(i, 16).NumberFormat = "dd/mm/yyyy"
        Else
            ws.Cells(i, 16).Value = "خطأ: " & textA
            ws.Cells(i, 16).Interior.Color = RGB(255, 200, 200)
        End If
        
        If convertI Then
            ws.Cells(i, 17).Value = dateI ' العمود Q
            ws.Cells(i, 17).NumberFormat = "dd/mm/yyyy"
        Else
            ws.Cells(i, 17).Value = "خطأ: " & textI
            ws.Cells(i, 17).Interior.Color = RGB(255, 200, 200)
        End If
        
        ' حساب الفرق والحالة
        If convertA And convertI Then
            dateDiff = dateI - dateA
            ws.Cells(i, 18).Value = dateDiff ' العمود R
            
            ' تحديد الحالة والتنسيق
            If dateDiff > 0 Then
                statusMsg = "متأخر " & dateDiff & " يوم"
                ws.Cells(i, 18).Interior.Color = RGB(255, 182, 193) ' أحمر فاتح
                ws.Cells(i, 19).Interior.Color = RGB(255, 182, 193)
            ElseIf dateDiff < 0 Then
                statusMsg = "مبكر " & Abs(dateDiff) & " يوم"
                ws.Cells(i, 18).Interior.Color = RGB(144, 238, 144) ' أخضر فاتح
                ws.Cells(i, 19).Interior.Color = RGB(144, 238, 144)
            Else
                statusMsg = "في الموعد"
                ws.Cells(i, 18).Interior.Color = RGB(255, 255, 224) ' أصفر فاتح
                ws.Cells(i, 19).Interior.Color = RGB(255, 255, 224)
            End If
            
            ws.Cells(i, 19).Value = statusMsg ' العمود S
            processedCount = processedCount + 1
        Else
            ws.Cells(i, 18).Value = "لا يمكن الحساب"
            ws.Cells(i, 19).Value = "خطأ في التاريخ"
            ws.Cells(i, 18).Interior.Color = RGB(255, 0, 0)
            ws.Cells(i, 19).Interior.Color = RGB(255, 0, 0)
            ws.Cells(i, 18).Font.Color = RGB(255, 255, 255)
            ws.Cells(i, 19).Font.Color = RGB(255, 255, 255)
            errorCount = errorCount + 1
        End If
    Next i
End Sub

' دالة محسنة لتحويل النص إلى تاريخ
Function ConvertTextToDateAdvanced(textValue As String, ByRef resultDate As Date) As Boolean
    Dim cleanText As String
    Dim dateParts As Variant
    Dim day As Integer, month As Integer, year As Integer
    
    ConvertTextToDateAdvanced = False
    cleanText = Trim(textValue)
    
    If cleanText = "" Or cleanText = "0" Then Exit Function
    
    ' محاولة التحويل المباشر أولاً
    On Error Resume Next
    resultDate = CDate(cleanText)
    If Err.Number = 0 And resultDate > DateSerial(1900, 1, 1) Then
        ConvertTextToDateAdvanced = True
        On Error GoTo 0
        Exit Function
    End If
    On Error GoTo 0
    
    ' تنظيف النص ومحاولة تحليله
    cleanText = Replace(cleanText, "-", "/")
    cleanText = Replace(cleanText, ".", "/")
    cleanText = Replace(cleanText, " ", "")
    
    ' التعامل مع الصيغ المختلفة
    If InStr(cleanText, "/") > 0 Then
        dateParts = Split(cleanText, "/")
        If UBound(dateParts) >= 2 Then
            On Error Resume Next
            day = CInt(dateParts(0))
            month = CInt(dateParts(1))
            year = CInt(dateParts(2))
            
            ' تصحيح السنة
            If year < 100 Then
                If year < 50 Then
                    year = year + 2000
                Else
                    year = year + 1900
                End If
            End If
            
            ' التحقق من صحة التاريخ
            If Err.Number = 0 And day >= 1 And day <= 31 And month >= 1 And month <= 12 And year >= 1900 And year <= 2100 Then
                resultDate = DateSerial(year, month, day)
                ConvertTextToDateAdvanced = True
            End If
            On Error GoTo 0
        End If
    End If
End Function

' دالة لتطبيق التنسيق المتقدم
Sub ApplyAdvancedFormatting(ws As Worksheet, lastRow As Long)
    ' تنسيق عام
    With ws.Range("A1:S" & lastRow)
        .Font.Name = "Calibri"
        .Font.Size = 10
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
        .Borders.Color = RGB(180, 180, 180)
    End With
    
    ' تنسيق الصف الأول
    With ws.Range("A1:S1")
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196)
        .Font.Color = RGB(255, 255, 255)
        .HorizontalAlignment = xlCenter
    End With
    
    ' ضبط عرض الأعمدة
    ws.Columns("A:S").AutoFit
    
    ' إضافة تصفية
    If lastRow > 1 Then
        ws.Range("A1:S" & lastRow).AutoFilter
    End If
    
    ' تجميد الصفوف
    ws.Range("A2").Select
    ActiveWindow.FreezePanes = True
End Sub

' دالة لعرض تقرير النتائج
Sub ShowProcessingReport(sheetName As String, totalRows As Long, processedCount As Long, errorCount As Long, ordersRow As Long)
    Dim reportMsg As String
    
    reportMsg = "تقرير معالجة البيانات:" & vbCrLf & vbCrLf & _
               "• اسم الشيت الجديد: " & sheetName & vbCrLf & _
               "• إجمالي الصفوف المنسوخة: " & totalRows & vbCrLf & _
               "• الصفوف المعالجة للتواريخ: " & (totalRows - 8) & vbCrLf & _
               "• التواريخ المعالجة بنجاح: " & processedCount & vbCrLf & _
               "• الأخطاء في التواريخ: " & errorCount & vbCrLf & _
               "• موقع Orders: الصف " & ordersRow & vbCrLf & vbCrLf & _
               "الأعمدة الجديدة:" & vbCrLf & _
               "• P: التاريخ المحول من العمود A" & vbCrLf & _
               "• Q: التاريخ المحول من العمود I" & vbCrLf & _
               "• R: الفرق بالأيام" & vbCrLf & _
               "• S: حالة التاريخ"
    
    MsgBox reportMsg, vbInformation, "تم إنجاز المعالجة"
End Sub

' دالة سريعة للاستخدام المباشر
Sub QuickProcessDates()
    Call AdvancedDateDifferenceProcessor
End Sub
