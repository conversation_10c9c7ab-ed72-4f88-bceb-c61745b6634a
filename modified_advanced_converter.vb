Sub ModifiedAdvancedDataConverter()
    Dim wsSource As Worksheet
    Dim wsNew As Worksheet
    Dim ordersRow As Long
    Dim lastRowToCopy As Long
    Dim sourceRange As Range
    Dim response As VbMsgBoxResult
    
    ' التحقق من وجود Sheet1
    On Error Resume Next
    Set wsSource = ThisWorkbook.Sheets("Sheet1")
    On Error GoTo 0
    
    If wsSource Is Nothing Then
        MsgBox "لم يتم العثور على Sheet1", vbCritical
        Exit Sub
    End If
    
    ' البحث عن كلمة "Orders"
    ordersRow = FindOrdersRow(wsSource)
    
    If ordersRow = 0 Then
        MsgBox "لم يتم العثور على كلمة 'Orders' في الشيت", vbExclamation
        Exit Sub
    End If
    
    ' تحديد آخر صف للنسخ (الصف الذي يسبق Orders)
    lastRowToCopy = ordersRow - 1
    
    If lastRowToCopy < 1 Then
        MsgBox "لا توجد بيانات للنسخ قبل صف Orders", vbExclamation
        Exit Sub
    End If
    
    ' تأكيد من المستخدم
    response = MsgBox("تم العثور على كلمة 'Orders' في الصف " & ordersRow & vbCrLf & _
                     "سيتم نسخ البيانات من الصف 1 إلى الصف " & lastRowToCopy & vbCrLf & _
                     "هل تريد المتابعة لإنشاء شيت جديد؟", vbYesNo + vbQuestion)
    
    If response = vbNo Then Exit Sub
    
    ' إنشاء الشيت الجديد
    Set wsNew = CreateNewSheet()
    
    ' نسخ وتحويل البيانات (بدون صف Orders)
    CopyAndConvertDataModified wsSource, wsNew, lastRowToCopy
    
    ' حذف العمودين M و N
    DeleteColumnsM_N wsNew
    
    ' إضافة كلمة "Profit" في الخلية M8
    wsNew.Range("M8").Value = "Profit"
    
    ' تنسيق الشيت الجديد
    FormatConvertedSheetModified wsNew, lastRowToCopy
    
    ' رسالة النجاح
    MsgBox "تم إنشاء الشيت الجديد بنجاح!" & vbCrLf & _
           "اسم الشيت: " & wsNew.Name & vbCrLf & _
           "البيانات المنسوخة: A1:O" & lastRowToCopy & vbCrLf & _
           "تم حذف العمودين M و N" & vbCrLf & _
           "تم إضافة 'Profit' في الخلية M8" & vbCrLf & _
           "تم تحويل النصوص إلى أرقام حيث أمكن", vbInformation
    
    ' تفعيل الشيت الجديد
    wsNew.Activate
    wsNew.Range("A1").Select
    
End Sub

' دالة للبحث عن صف "Orders"
Function FindOrdersRow(ws As Worksheet) As Long
    Dim i As Long, j As Long
    Dim cellValue As String
    
    FindOrdersRow = 0
    
    ' البحث في الصفوف من 1 إلى 1000
    For i = 1 To 1000
        For j = 1 To 15 ' من العمود A إلى O
            cellValue = Trim(CStr(ws.Cells(i, j).Value))
            If InStr(1, cellValue, "Orders", vbTextCompare) > 0 Then
                FindOrdersRow = i
                Exit Function
            End If
        Next j
    Next i
End Function

' دالة لإنشاء شيت جديد
Function CreateNewSheet() As Worksheet
    Dim newSheetName As String
    Dim counter As Integer
    
    counter = 1
    newSheetName = "ProcessedData"
    
    ' التأكد من عدم وجود شيت بنفس الاسم
    Do While SheetExists(newSheetName)
        newSheetName = "ProcessedData_" & counter
        counter = counter + 1
    Loop
    
    Set CreateNewSheet = ThisWorkbook.Sheets.Add
    CreateNewSheet.Name = newSheetName
End Function

' دالة للتحقق من وجود شيت
Function SheetExists(sheetName As String) As Boolean
    Dim ws As Worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets(sheetName)
    SheetExists = Not ws Is Nothing
    On Error GoTo 0
End Function

' دالة لنسخ وتحويل البيانات (معدلة)
Sub CopyAndConvertDataModified(wsSource As Worksheet, wsTarget As Worksheet, lastRow As Long)
    Dim i As Long, j As Long
    Dim cellValue As Variant
    Dim convertedValue As Variant

    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual

    ' نسخ الخلايا من A1 إلى A7 بنفس التنسيق دون تغيير
    If lastRow >= 7 Then
        wsSource.Range("A1:A7").Copy
        wsTarget.Range("A1:A7").PasteSpecial xlPasteAll
        Application.CutCopyMode = False
    End If

    ' نسخ باقي البيانات مع التحويل (من الصف 1 إلى lastRow)
    For i = 1 To lastRow
        For j = 1 To 15 ' من العمود A إلى O
            ' تخطي العمود A للصفوف من 1 إلى 7 (تم نسخها بالفعل)
            If j = 1 And i <= 7 Then
                GoTo NextColumn
            End If

            cellValue = wsSource.Cells(i, j).Value
            convertedValue = ConvertCellValue(cellValue)
            wsTarget.Cells(i, j).Value = convertedValue

NextColumn:
        Next j
    Next i

    Application.Calculation = xlCalculationAutomatic
    Application.ScreenUpdating = True
End Sub

' دالة لتحويل قيمة الخلية
Function ConvertCellValue(cellValue As Variant) As Variant
    Dim tempValue As String
    Dim numericValue As Double
    
    ' إذا كانت الخلية فارغة
    If IsEmpty(cellValue) Or cellValue = "" Then
        ConvertCellValue = ""
        Exit Function
    End If
    
    tempValue = Trim(CStr(cellValue))
    
    ' محاولة تحويل إلى رقم
    If IsNumeric(tempValue) And tempValue <> "" Then
        numericValue = CDbl(tempValue)
        ConvertCellValue = numericValue
    ElseIf IsDate(tempValue) Then
        ' تحويل إلى تاريخ
        ConvertCellValue = CDate(tempValue)
    Else
        ' الاحتفاظ بالنص كما هو
        ConvertCellValue = tempValue
    End If
End Function

' دالة لحذف العمودين M و N
Sub DeleteColumnsM_N(ws As Worksheet)
    Application.DisplayAlerts = False
    
    ' حذف العمود N أولاً (العمود 14)
    ws.Columns(14).Delete
    
    ' حذف العمود M (العمود 13)
    ws.Columns(13).Delete
    
    Application.DisplayAlerts = True
End Sub

' دالة لتنسيق الشيت المحول (معدلة)
Sub FormatConvertedSheetModified(ws As Worksheet, lastRow As Long)
    Dim headerRange As Range
    Dim dataRange As Range
    Dim dataRangeExcludingA1A7 As Range

    ' تحديد النطاقات (بعد حذف العمودين M و N)
    Set headerRange = ws.Range("B1:M1") ' تجنب A1 لأنه تم نسخه بالتنسيق الأصلي
    Set dataRange = ws.Range("A1:M" & lastRow) ' أصبح M بدلاً من O

    ' تنسيق عام للبيانات (تجنب A1:A7)
    ' تنسيق من B1 إلى M7
    If lastRow >= 7 Then
        Set dataRangeExcludingA1A7 = ws.Range("B1:M7")
        With dataRangeExcludingA1A7
            .Font.Name = "Calibri"
            .Font.Size = 11
            .Borders.LineStyle = xlContinuous
            .Borders.Weight = xlThin
            .Borders.Color = RGB(200, 200, 200)
        End With
    End If

    ' تنسيق من A8 إلى M(lastRow) إذا كان هناك صفوف بعد الصف 7
    If lastRow > 7 Then
        Set dataRangeExcludingA1A7 = ws.Range("A8:M" & lastRow)
        With dataRangeExcludingA1A7
            .Font.Name = "Calibri"
            .Font.Size = 11
            .Borders.LineStyle = xlContinuous
            .Borders.Weight = xlThin
            .Borders.Color = RGB(200, 200, 200)
        End With
    End If

    ' تنسيق العناوين (تجنب A1)
    With headerRange
        .Font.Bold = True
        .Font.Color = RGB(255, 255, 255)
        .Interior.Color = RGB(68, 114, 196)
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With

    ' تنسيق خاص لخلية "Profit"
    With ws.Range("M8")
        .Font.Bold = True
        .Font.Color = RGB(0, 128, 0) ' أخضر
        .Interior.Color = RGB(220, 255, 220) ' خلفية خضراء فاتحة
        .HorizontalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlMedium
        .Borders.Color = RGB(0, 128, 0)
    End With

    ' ضبط عرض الأعمدة (تجنب العمود A)
    ws.Columns("B:M").AutoFit

    ' إضافة تصفية تلقائية
    If lastRow > 1 Then
        dataRange.AutoFilter
    End If

    ' تجميد الصف الأول
    ws.Range("A2").Select
    ActiveWindow.FreezePanes = True

End Sub

' دالة سريعة للتشغيل المباشر
Sub QuickModifiedConvert()
    Call ModifiedAdvancedDataConverter
End Sub
