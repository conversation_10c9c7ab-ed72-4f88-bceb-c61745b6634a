حلول مشكلة عدم ظهور النص العربي في VBA عند النقل لجهاز آخر:

=== الحل الأول: إعدادات النظام ===

1. تأكد من تثبيت اللغة العربية في النظام:
   - Control Panel > Region > Administrative > Change system locale
   - اختر Arabic (Saudi Arabia) أو Arabic (Egypt)
   - أعد تشغيل الجهاز

2. تأكد من إعدادات الخط في Excel:
   - File > Options > Advanced > Web Options > Fonts
   - اختر خط يدعم العربية مثل Arial أو Tahoma

=== الحل الثاني: حفظ الملف بالترميز الصحيح ===

1. احفظ ملف Excel بصيغة .xlsm مع ترميز Unicode
2. تأكد من أن الملف محفوظ بـ UTF-8 encoding

=== الحل الثالث: استخدام API Windows ===

استخدم MessageBoxW بدلاً من MsgBox العادي:

Private Declare PtrSafe Function MessageBoxW Lib "user32" ( _
    ByVal hWnd As LongPtr, _
    ByVal lpText As LongPtr, _
    ByVal lpCaption As LongPtr, _
    ByVal wType As Long) As Long

Private Sub ShowArabicMessage(msg As String, title As String, msgType As Long)
    MessageBoxW 0, StrPtr(msg), StrPtr(title), msgType
End Sub

=== الحل الرابع: فحص دعم Unicode ===

أضف هذا الكود لفحص دعم النظام:

Private Sub TestUnicodeSupport()
    Dim testChar As String
    testChar = ChrW(1575) ' حرف الألف
    If Len(testChar) = 1 Then
        MsgBox "النظام يدعم Unicode"
    Else
        MsgBox "النظام لا يدعم Unicode بشكل صحيح"
    End If
End Sub

=== الحل الخامس: استخدام ملف خارجي للنصوص ===

1. أنشئ ملف نصي منفصل يحتوي على النصوص العربية
2. اقرأ النصوص من الملف في VBA
3. هذا يضمن عدم فقدان الترميز

=== نصائح إضافية ===

1. تجنب نسخ ولصق الكود - استخدم Import/Export
2. تأكد من أن جميع الأجهزة تستخدم نفس إصدار Office
3. استخدم خطوط Unicode مثل Arial Unicode MS
4. تجنب استخدام أحرف خاصة في أسماء المتغيرات

=== اختبار سريع ===

لاختبار ما إذا كان النظام يعرض العربية بشكل صحيح:
MsgBox ChrW(1575) & ChrW(1604) & ChrW(1587) & ChrW(1604) & ChrW(1575) & ChrW(1605) & " " & ChrW(1593) & ChrW(1604) & ChrW(1610) & ChrW(1603) & ChrW(1605)

إذا ظهرت "السلام عليكم" بشكل صحيح، فالنظام يدعم العربية.
