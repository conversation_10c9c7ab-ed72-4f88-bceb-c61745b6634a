Private Sub CommandButton2_Click()
    Dim txt As String
    Dim val5 As Double, val6 As Double
    Dim totalValue As Double
    Dim selectedRank As String
    txt = Me.TextBox7.Value
    totalValue = 0
    selectedRank = Me.ComboBox1.Value
    
    If Trim(Me.TextBox12.Value) = "" Or Not IsNumeric(Replace(Me.TextBox12.Value, "$", "")) Then
        ' يرجى اختيار مستوى عرض النشر اليومي.
        ' خطأ
        MsgBox ChrW(1610) & ChrW(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1605) & ChrW(1587) & ChrW(1578) & ChrW(1608) & ChrW(1609) & " " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1575) & ChrW(1604) & ChrW(1610) & ChrW(1608) & ChrW(1605) & ChrW(1610) & ".", vbCritical, ChrW(1582) & ChrW(1591) & ChrW(1571)
        Me.TextBox12.SetFocus
        Exit Sub
    End If
    
    val5 = CDbl(Replace(Me.TextBox12.Value, "$", ""))
    
    If IsNumeric(Replace(Me.TextBox13.Value, "$", "")) Then
        val6 = CDbl(Replace(Me.TextBox13.Value, "$", ""))
    Else
        val6 = 0
    End If
    
    ' عرض نشر يومي: 30 / 30
    If InStr(txt, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1610) & ChrW(1608) & ChrW(1605) & ChrW(1610) & ": 30 / 30") > 0 Then
        totalValue = val5
    End If
    
    If Not (selectedRank = "Beginning" Or selectedRank = "Growth") Then
        ' عرض التحليلات الأسبوعية: 1 / 4
        If InStr(txt, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577) & ": 1 / 4") > 0 Then totalValue = totalValue + val6
        
        ' عرض التحليلات الأسبوعية: 2 / 4
        If InStr(txt, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577) & ": 2 / 4") > 0 Then totalValue = totalValue + (2 * val6)
        
        ' عرض التحليلات الأسبوعية: 3 / 4
        If InStr(txt, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577) & ": 3 / 4") > 0 Then totalValue = totalValue + (3 * val6)
        
        ' عرض التحليلات الأسبوعية: 4 / 4
        If InStr(txt, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577) & ": 4 / 4") > 0 Then totalValue = totalValue + (4 * val6)
    End If
    
    Me.TextBox8.Value = Format(totalValue, "$#,##0.00")
End Sub

' ملاحظات حول التحويل:
'
' النص الأول: "يرجى اختيار مستوى عرض النشر اليومي."
' تم تحويله إلى:
' ChrW(1610) & ChrW(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1605) & ChrW(1587) & ChrW(1578) & ChrW(1608) & ChrW(1609) & " " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1575) & ChrW(1604) & ChrW(1610) & ChrW(1608) & ChrW(1605) & ChrW(1610) & "."
'
' النص الثاني: "خطأ"
' تم تحويله إلى:
' ChrW(1582) & ChrW(1591) & ChrW(1571)
'
' النص الثالث: "عرض نشر يومي: 30 / 30"
' تم تحويله إلى:
' ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1610) & ChrW(1608) & ChrW(1605) & ChrW(1610) & ": 30 / 30"
'
' النص الرابع: "عرض التحليلات الأسبوعية"
' تم تحويله إلى:
' ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577)
'
' رموز Unicode الجديدة المستخدمة:
' س = ChrW(1587)  |  خ = ChrW(1582)  |  ط = ChrW(1591)
' جميع الرموز الأخرى مستخدمة من الأكواد السابقة
