Sub SimpleDataConverter()
    ' نسخة مبسطة لتحويل البيانات من Sheet1 إلى شيت جديد
    
    Dim ws1 As Worksheet, ws2 As Worksheet
    Dim ordersRow As Long, i <PERSON>, j As Long
    Dim searchRange As Range, foundCell As Range
    
    ' تعيين Sheet1
    Set ws1 = ThisWorkbook.Sheets("Sheet1")
    
    ' البحث عن كلمة "Orders"
    Set searchRange = ws1.Range("A:O")
    Set foundCell = searchRange.Find("Orders", , xlValues, xlPart, , , False)
    
    If foundCell Is Nothing Then
        MsgBox "لم يتم العثور على كلمة Orders"
        Exit Sub
    End If
    
    ordersRow = foundCell.Row
    
    ' إنشاء شيت جديد
    Set ws2 = Sheets.Add
    ws2.Name = "ConvertedData"
    
    ' نسخ البيانات مع التحويل
    For i = 1 To ordersRow
        For j = 1 To 15 ' A إلى O
            Dim cellVal As Variant
            cellVal = ws1.Cells(i, j).Value
            
            ' تحويل النص إلى رقم إذا أمكن
            If IsNumeric(cellVal) And cellVal <> "" Then
                ws2.Cells(i, j).Value = CDbl(cellVal)
            Else
                ws2.Cells(i, j).Value = cellVal
            End If
        Next j
    Next i
    
    ' تنسيق بسيط
    ws2.Range("A1:O1").Font.Bold = True
    ws2.Columns("A:O").AutoFit
    
    MsgBox "تم التحويل بنجاح! الشيت الجديد: " & ws2.Name
    ws2.Activate
    
End Sub

' دالة للتحويل مع حفظ التنسيق
Sub ConvertWithFormatting()
    Dim ws1 As Worksheet, ws2 As Worksheet
    Dim ordersRow As Long
    Dim sourceRange As Range, targetRange As Range
    
    Set ws1 = ThisWorkbook.Sheets("Sheet1")
    
    ' البحث عن "Orders"
    Dim foundCell As Range
    Set foundCell = ws1.Range("A:O").Find("Orders", , xlValues, xlPart)
    
    If foundCell Is Nothing Then
        MsgBox "كلمة Orders غير موجودة"
        Exit Sub
    End If
    
    ordersRow = foundCell.Row
    
    ' إنشاء شيت جديد
    Set ws2 = Sheets.Add
    ws2.Name = "FormattedData"
    
    ' تحديد النطاق المصدر والهدف
    Set sourceRange = ws1.Range("A1:O" & ordersRow)
    Set targetRange = ws2.Range("A1:O" & ordersRow)
    
    ' نسخ القيم والتنسيق
    sourceRange.Copy
    targetRange.PasteSpecial xlPasteValues
    targetRange.PasteSpecial xlPasteFormats
    
    ' تحويل النصوص إلى أرقام
    Dim cell As Range
    For Each cell In targetRange
        If IsNumeric(cell.Value) And cell.Value <> "" Then
            cell.Value = CDbl(cell.Value)
        End If
    Next cell
    
    Application.CutCopyMode = False
    
    ' تحسين التنسيق
    With targetRange
        .Borders.LineStyle = xlContinuous
        .Font.Name = "Arial"
    End With
    
    ws2.Range("A1:O1").Interior.Color = RGB(200, 220, 255)
    ws2.Columns("A:O").AutoFit
    
    MsgBox "تم إنشاء " & ws2.Name & " بنجاح مع التنسيق"
    ws2.Activate
    
End Sub

' دالة للتحويل مع إزالة النصوص الفارغة
Sub ConvertAndCleanData()
    Dim ws1 As Worksheet, ws2 As Worksheet
    Dim ordersRow As Long, i As Long, j As Long
    Dim cellValue As Variant
    
    Set ws1 = ThisWorkbook.Sheets("Sheet1")
    
    ' البحث عن Orders
    ordersRow = 0
    For i = 1 To 1000
        For j = 1 To 15
            If InStr(1, CStr(ws1.Cells(i, j).Value), "Orders", vbTextCompare) > 0 Then
                ordersRow = i
                GoTo FoundOrders
            End If
        Next j
    Next i
    
FoundOrders:
    If ordersRow = 0 Then
        MsgBox "لم يتم العثور على Orders"
        Exit Sub
    End If
    
    ' إنشاء شيت جديد
    Set ws2 = Sheets.Add
    ws2.Name = "CleanedData"
    
    ' نسخ وتنظيف البيانات
    For i = 1 To ordersRow
        For j = 1 To 15
            cellValue = ws1.Cells(i, j).Value
            
            ' تنظيف وتحويل البيانات
            If IsEmpty(cellValue) Or cellValue = "" Then
                ws2.Cells(i, j).Value = ""
            ElseIf IsNumeric(cellValue) Then
                ws2.Cells(i, j).Value = CDbl(cellValue)
            ElseIf IsDate(cellValue) Then
                ws2.Cells(i, j).Value = CDate(cellValue)
            Else
                ' إزالة المسافات الزائدة
                ws2.Cells(i, j).Value = Trim(CStr(cellValue))
            End If
        Next j
    Next i
    
    ' تنسيق الشيت
    With ws2.Range("A1:O" & ordersRow)
        .Font.Name = "Calibri"
        .Font.Size = 10
        .Borders.LineStyle = xlContinuous
    End With
    
    ' تنسيق العناوين
    With ws2.Range("A1:O1")
        .Font.Bold = True
        .Interior.Color = RGB(180, 200, 220)
        .HorizontalAlignment = xlCenter
    End With
    
    ws2.Columns("A:O").AutoFit
    
    MsgBox "تم تنظيف وتحويل البيانات في " & ws2.Name
    ws2.Activate
    
End Sub
