salary = CDbl(Replace(Me.TextBox10.Value, "$", ""))
totalResult = 0
If InStr(Me.TextBox7.Value, "- عرض نشر يومي: 30 / 30 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- عرض بوستات إثبات السحب: 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- منشورات أسباب اختيار إنزو: 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- ندوة لايف: 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.1)
If InStr(Me.TextBox7.Value, "- عرض ريلزرات تفاعلية: 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.08)
If InStr(Me.TextBox7.Value, "- عرض الدورات التعليمية: 5 / 5 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- عرض التحليلات الأسبوعية: 4 / 4 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- نشر إثبات سحب - بونص ترحيبي: 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- تعاون مع شركة: 1 / 1 ") > 0 Then totalResult = totalResult + (salary * 0.1)
Me.TextBox9.Value = Format(totalResult, "$#,##0.00")
