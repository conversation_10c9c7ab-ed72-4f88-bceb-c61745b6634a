salary = CDbl(Replace(Me.TextBox10.Value, "$", ""))
totalResult = 0
If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1610) & ChrW(1608) & ChrW(1605) & ChrW(1610) & ": 30 / 30 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1576) & ChrW(1608) & ChrW(1587) & ChrW(1578) & ChrW(1575) & ChrW(1578) & " " & ChrW(1573) & ChrW(1579) & ChrW(1576) & ChrW(1575) & ChrW(1578) & " " & Chr<PERSON>(1575) & ChrW(1604) & ChrW(1587) & ChrW(1581) & ChrW(1576) & ": 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- " & ChrW(1605) & ChrW(1606) & ChrW(1588) & ChrW(1608) & ChrW(1585) & ChrW(1575) & ChrW(1578) & " " & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1575) & ChrW(1576) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1573) & ChrW(1606) & ChrW(1586) & ChrW(1608) & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- " & ChrW(1606) & ChrW(1583) & ChrW(1608) & ChrW(1577) & " " & ChrW(1604) & ChrW(1575) & ChrW(1610) & ChrW(1601) & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.1)
If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1585) & ChrW(1610) & ChrW(1604) & ChrW(1586) & ChrW(1585) & ChrW(1575) & ChrW(1578) & " " & ChrW(1578) & ChrW(1601) & ChrW(1575) & ChrW(1593) & ChrW(1604) & ChrW(1610) & ChrW(1577) & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.08)
If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1583) & ChrW(1608) & ChrW(1585) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1593) & ChrW(1604) & ChrW(1610) & ChrW(1605) & ChrW(1610) & ChrW(1577) & ": 5 / 5 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577) & ": 4 / 4 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- " & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1573) & ChrW(1579) & ChrW(1576) & ChrW(1575) & ChrW(1578) & " " & ChrW(1587) & ChrW(1581) & ChrW(1576) & " - " & ChrW(1576) & ChrW(1608) & ChrW(1606) & ChrW(1589) & " " & ChrW(1578) & ChrW(1585) & ChrW(1581) & ChrW(1610) & ChrW(1576) & ChrW(1610) & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- " & ChrW(1578) & ChrW(1593) & ChrW(1575) & ChrW(1608) & ChrW(1606) & " " & ChrW(1605) & ChrW(1593) & " " & ChrW(1588) & ChrW(1585) & ChrW(1603) & ChrW(1577) & ": 1 / 1 ") > 0 Then totalResult = totalResult + (salary * 0.1)
Me.TextBox9.Value = Format(totalResult, "$#,##0.00")
