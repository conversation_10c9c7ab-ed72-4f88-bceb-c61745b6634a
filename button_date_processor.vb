' كود للزر - يوضع في الشيت أو في Module
Sub ButtonDateProcessor()
    ' كود مخصص للزر لمعالجة البيانات وحساب فرق التواريخ
    
    Dim ws As Worksheet
    Dim ordersCell As Range
    Dim ordersRow As Long, lastRow As Long
    Dim startRow As Long
    Dim i As Long
    Dim dateA As Date, dateI As Date
    Dim textA As String, textI As String
    Dim daysDiff As Long
    Dim successCount As Long, errorCount As Long
    
    ' تعيين الشيت الحالي
    Set ws = ActiveSheet
    startRow = 9 ' البداية من الصف 9
    
    ' البحث عن كلمة Orders
    Set ordersCell = ws.Range("A:O").Find("Orders", , xlValues, xlPart)
    
    If ordersCell Is Nothing Then
        MsgBox "تعذر العثور على كلمة 'Orders' في النطاق A:O" & vbCrLf & _
               "تأكد من وجود الكلمة في الشيت", vbCritical, "خطأ"
        Exit Sub
    End If
    
    ordersRow = ordersCell.Row
    lastRow = ordersRow - 1
    
    ' التحقق من وجود بيانات كافية
    If lastRow < startRow Then
        MsgBox "لا توجد بيانات كافية للمعالجة" & vbCrLf & _
               "Orders موجود في الصف " & ordersRow & vbCrLf & _
               "يجب أن يكون هناك بيانات من الصف " & startRow & " على الأقل", _
               vbExclamation, "بيانات غير كافية"
        Exit Sub
    End If
    
    ' تأكيد من المستخدم
    Dim userResponse As VbMsgBoxResult
    userResponse = MsgBox("سيتم معالجة البيانات كالتالي:" & vbCrLf & vbCrLf & _
                         "• نسخ البيانات من A1 إلى O" & lastRow & vbCrLf & _
                         "• حساب فرق التواريخ من الصف " & startRow & " إلى " & lastRow & vbCrLf & _
                         "• العمود A: التاريخ الأول" & vbCrLf & _
                         "• العمود I: التاريخ الثاني" & vbCrLf & _
                         "• النتيجة: ستظهر في عمود جديد" & vbCrLf & vbCrLf & _
                         "هل تريد المتابعة؟", _
                         vbYesNo + vbQuestion, "تأكيد المعالجة")
    
    If userResponse = vbNo Then Exit Sub
    
    ' تعطيل تحديث الشاشة لتسريع العملية
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    ' إضافة عمود للنتائج
    ws.Range("P8").Value = "Days Difference (I - A)"
    With ws.Range("P8")
        .Font.Bold = True
        .Font.Size = 11
        .Interior.Color = RGB(255, 255, 0) ' أصفر
        .HorizontalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
    End With
    
    ' إضافة عمود للحالة
    ws.Range("Q8").Value = "Status"
    With ws.Range("Q8")
        .Font.Bold = True
        .Font.Size = 11
        .Interior.Color = RGB(255, 192, 203) ' وردي
        .HorizontalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
    End With
    
    successCount = 0
    errorCount = 0
    
    ' معالجة كل صف من startRow إلى lastRow
    For i = startRow To lastRow
        textA = Trim(CStr(ws.Cells(i, 1).Value)) ' العمود A
        textI = Trim(CStr(ws.Cells(i, 9).Value)) ' العمود I
        
        ' محاولة تحويل النصوص إلى تواريخ
        If ParseDateFromText(textA, dateA) And ParseDateFromText(textI, dateI) Then
            ' حساب الفرق بالأيام
            daysDiff = dateI - dateA
            
            ' كتابة النتيجة
            ws.Cells(i, 16).Value = daysDiff ' العمود P
            
            ' تحديد الحالة والتنسيق
            If daysDiff > 0 Then
                ws.Cells(i, 17).Value = "متأخر " & daysDiff & " يوم" ' العمود Q
                ws.Cells(i, 16).Interior.Color = RGB(255, 182, 193) ' أحمر فاتح
                ws.Cells(i, 17).Interior.Color = RGB(255, 182, 193)
            ElseIf daysDiff < 0 Then
                ws.Cells(i, 17).Value = "مبكر " & Abs(daysDiff) & " يوم"
                ws.Cells(i, 16).Interior.Color = RGB(144, 238, 144) ' أخضر فاتح
                ws.Cells(i, 17).Interior.Color = RGB(144, 238, 144)
            Else
                ws.Cells(i, 17).Value = "في الموعد المحدد"
                ws.Cells(i, 16).Interior.Color = RGB(255, 255, 224) ' أصفر فاتح
                ws.Cells(i, 17).Interior.Color = RGB(255, 255, 224)
            End If
            
            ' تنسيق النص
            ws.Cells(i, 16).HorizontalAlignment = xlCenter
            ws.Cells(i, 17).HorizontalAlignment = xlCenter
            ws.Cells(i, 16).Font.Bold = True
            
            successCount = successCount + 1
        Else
            ' في حالة فشل تحويل التاريخ
            ws.Cells(i, 16).Value = "خطأ"
            ws.Cells(i, 17).Value = "تاريخ غير صحيح"
            ws.Cells(i, 16).Interior.Color = RGB(255, 0, 0) ' أحمر
            ws.Cells(i, 17).Interior.Color = RGB(255, 0, 0)
            ws.Cells(i, 16).Font.Color = RGB(255, 255, 255) ' نص أبيض
            ws.Cells(i, 17).Font.Color = RGB(255, 255, 255)
            ws.Cells(i, 16).HorizontalAlignment = xlCenter
            ws.Cells(i, 17).HorizontalAlignment = xlCenter
            
            errorCount = errorCount + 1
        End If
    Next i
    
    ' تنسيق الأعمدة الجديدة
    ws.Columns("P:Q").AutoFit
    
    ' إعادة تفعيل تحديث الشاشة
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    
    ' عرض تقرير النتائج
    Dim reportMessage As String
    reportMessage = "تم إنجاز المعالجة بنجاح!" & vbCrLf & vbCrLf & _
                   "النتائج:" & vbCrLf & _
                   "• إجمالي الصفوف المعالجة: " & (lastRow - startRow + 1) & vbCrLf & _
                   "• التواريخ المعالجة بنجاح: " & successCount & vbCrLf & _
                   "• الأخطاء في التواريخ: " & errorCount & vbCrLf & vbCrLf & _
                   "تم إضافة النتائج في:" & vbCrLf & _
                   "• العمود P: فرق الأيام" & vbCrLf & _
                   "• العمود Q: حالة التاريخ" & vbCrLf & vbCrLf & _
                   "الألوان:" & vbCrLf & _
                   "• أحمر: متأخر" & vbCrLf & _
                   "• أخضر: مبكر" & vbCrLf & _
                   "• أصفر: في الموعد"
    
    MsgBox reportMessage, vbInformation, "تم إنجاز العملية"
    
End Sub

' دالة لتحليل النص وتحويله إلى تاريخ
Function ParseDateFromText(textValue As String, ByRef resultDate As Date) As Boolean
    Dim cleanText As String
    Dim dateParts As Variant
    Dim day As Integer, month As Integer, year As Integer
    
    ParseDateFromText = False
    cleanText = Trim(textValue)
    
    ' التحقق من وجود قيمة
    If cleanText = "" Or cleanText = "0" Or IsEmpty(textValue) Then
        Exit Function
    End If
    
    ' محاولة التحويل المباشر أولاً
    On Error Resume Next
    resultDate = CDate(cleanText)
    If Err.Number = 0 And resultDate >= DateSerial(1900, 1, 1) And resultDate <= DateSerial(2100, 12, 31) Then
        ParseDateFromText = True
        On Error GoTo 0
        Exit Function
    End If
    On Error GoTo 0
    
    ' تنظيف النص
    cleanText = Replace(cleanText, "-", "/")
    cleanText = Replace(cleanText, ".", "/")
    cleanText = Replace(cleanText, " ", "")
    
    ' تحليل التاريخ يدوياً
    If InStr(cleanText, "/") > 0 Then
        dateParts = Split(cleanText, "/")
        
        If UBound(dateParts) >= 2 Then
            On Error Resume Next
            
            ' قراءة الأجزاء
            day = CInt(dateParts(0))
            month = CInt(dateParts(1))
            year = CInt(dateParts(2))
            
            ' تصحيح السنة إذا كانت مختصرة
            If year < 100 Then
                If year < 50 Then
                    year = year + 2000
                Else
                    year = year + 1900
                End If
            End If
            
            ' التحقق من صحة القيم
            If Err.Number = 0 And _
               day >= 1 And day <= 31 And _
               month >= 1 And month <= 12 And _
               year >= 1900 And year <= 2100 Then
                
                ' إنشاء التاريخ
                resultDate = DateSerial(year, month, day)
                ParseDateFromText = True
            End If
            
            On Error GoTo 0
        End If
    End If
End Function

' دالة مبسطة جداً للاستخدام السريع
Sub SimpleButtonProcessor()
    Dim ws As Worksheet
    Dim ordersRow As Long, i As Long
    Dim foundCell As Range
    
    Set ws = ActiveSheet
    
    ' البحث عن Orders
    Set foundCell = ws.Range("A:O").Find("Orders")
    If foundCell Is Nothing Then
        MsgBox "Orders غير موجود"
        Exit Sub
    End If
    
    ordersRow = foundCell.Row
    
    ' إضافة عمود النتائج
    ws.Range("P8").Value = "فرق الأيام"
    ws.Range("P8").Font.Bold = True
    
    ' حساب الفرق لكل صف
    For i = 9 To ordersRow - 1
        Dim dateA As Date, dateI As Date
        
        If IsDate(ws.Cells(i, 1).Value) And IsDate(ws.Cells(i, 9).Value) Then
            dateA = CDate(ws.Cells(i, 1).Value)
            dateI = CDate(ws.Cells(i, 9).Value)
            ws.Cells(i, 16).Value = dateI - dateA
        Else
            ws.Cells(i, 16).Value = "خطأ"
        End If
    Next i
    
    MsgBox "تم حساب فرق التواريخ"
End Sub
