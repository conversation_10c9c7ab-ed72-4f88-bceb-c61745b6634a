Sub CopyDataToNewSheet()
    Dim wsSource As Worksheet
    Dim wsNew As Worksheet
    Dim lastRow As Long
    Dim ordersRow As Long
    Dim sourceRange As Range
    Dim targetRange As Range
    Dim i As Long, j As Long
    Dim cellValue As Variant
    
    ' تعيين الشيت المصدر
    Set wsSource = ThisWorkbook.Sheets("Sheet1")
    
    ' البحث عن الصف الذي يحتوي على كلمة "Orders"
    ordersRow = 0
    For i = 1 To wsSource.Rows.Count
        If InStr(1, wsSource.Cells(i, 1).Value, "Orders", vbTextCompare) > 0 Then
            ordersRow = i
            Exit For
        End If
        ' البحث في جميع الأعمدة من A إلى O
        For j = 1 To 15 ' العمود O هو العمود رقم 15
            If InStr(1, wsSource.Cells(i, j).Value, "Orders", vbTextCompare) > 0 Then
                ordersRow = i
                Exit For
            End If
        Next j
        If ordersRow > 0 Then Exit For
    Next i
    
    ' التحقق من وجود كلمة "Orders"
    If ordersRow = 0 Then
        MsgBox "لم يتم العثور على كلمة 'Orders' في الشيت", vbExclamation
        Exit Sub
    End If
    
    ' إنشاء شيت جديد
    Set wsNew = ThisWorkbook.Sheets.Add
    wsNew.Name = "ProcessedData_" & Format(Now, "yyyymmdd_hhmmss")
    
    ' تحديد النطاق المصدر (من A1 إلى O والصف الذي يحتوي على Orders)
    Set sourceRange = wsSource.Range("A1:O" & ordersRow)
    
    ' نسخ البيانات مع تحويلها لتقبل المعادلات
    For i = 1 To sourceRange.Rows.Count
        For j = 1 To sourceRange.Columns.Count
            cellValue = sourceRange.Cells(i, j).Value
            
            ' تحويل النص إلى رقم إذا كان ممكناً
            If IsNumeric(cellValue) And cellValue <> "" Then
                ' تحويل النص إلى رقم
                wsNew.Cells(i, j).Value = CDbl(cellValue)
            ElseIf IsDate(cellValue) Then
                ' تحويل النص إلى تاريخ
                wsNew.Cells(i, j).Value = CDate(cellValue)
            Else
                ' نسخ النص كما هو
                wsNew.Cells(i, j).Value = cellValue
            End If
        Next j
    Next i
    
    ' تنسيق الشيت الجديد
    With wsNew.Range("A1:O" & ordersRow)
        .Font.Name = "Arial"
        .Font.Size = 10
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
    End With
    
    ' تنسيق الصف الأول (العناوين)
    With wsNew.Range("A1:O1")
        .Font.Bold = True
        .Interior.Color = RGB(200, 200, 200)
        .HorizontalAlignment = xlCenter
    End With
    
    ' ضبط عرض الأعمدة تلقائياً
    wsNew.Columns("A:O").AutoFit
    
    ' رسالة تأكيد
    MsgBox "تم نسخ البيانات بنجاح إلى الشيت الجديد: " & wsNew.Name & vbCrLf & _
           "عدد الصفوف المنسوخة: " & ordersRow & vbCrLf & _
           "النطاق: A1:O" & ordersRow, vbInformation
    
    ' تفعيل الشيت الجديد
    wsNew.Activate
    wsNew.Range("A1").Select
    
End Sub

' دالة مساعدة للبحث عن كلمة "Orders" بطريقة أكثر دقة
Sub FindAndCopyOrders()
    Dim wsSource As Worksheet
    Dim wsNew As Worksheet
    Dim searchRange As Range
    Dim foundCell As Range
    Dim ordersRow As Long
    Dim sourceRange As Range
    
    Set wsSource = ThisWorkbook.Sheets("Sheet1")
    
    ' البحث عن كلمة "Orders" في النطاق A:O
    Set searchRange = wsSource.Range("A:O")
    Set foundCell = searchRange.Find("Orders", LookIn:=xlValues, LookAt:=xlPart, MatchCase:=False)
    
    If foundCell Is Nothing Then
        MsgBox "لم يتم العثور على كلمة 'Orders' في النطاق المحدد", vbExclamation
        Exit Sub
    End If
    
    ordersRow = foundCell.Row
    
    ' إنشاء شيت جديد
    Set wsNew = ThisWorkbook.Sheets.Add
    wsNew.Name = "ConvertedData_" & Format(Now, "yyyymmdd_hhmmss")
    
    ' نسخ البيانات مع التحويل
    Set sourceRange = wsSource.Range("A1:O" & ordersRow)
    
    ' نسخ القيم مع التحويل التلقائي
    wsNew.Range("A1:O" & ordersRow).Value = sourceRange.Value
    
    ' تحويل النصوص إلى أرقام حيث أمكن
    ConvertTextToNumbers wsNew.Range("A1:O" & ordersRow)
    
    ' تنسيق الشيت
    FormatNewSheet wsNew, ordersRow
    
    MsgBox "تم إنشاء الشيت الجديد بنجاح: " & wsNew.Name, vbInformation
    wsNew.Activate
    
End Sub

' دالة لتحويل النصوص إلى أرقام
Sub ConvertTextToNumbers(targetRange As Range)
    Dim cell As Range
    Dim tempValue As Variant
    
    For Each cell In targetRange
        If cell.Value <> "" Then
            tempValue = cell.Value
            
            ' محاولة تحويل النص إلى رقم
            If IsNumeric(tempValue) Then
                cell.Value = CDbl(tempValue)
                cell.NumberFormat = "General"
            End If
        End If
    Next cell
End Sub

' دالة لتنسيق الشيت الجديد
Sub FormatNewSheet(ws As Worksheet, lastRow As Long)
    With ws.Range("A1:O" & lastRow)
        .Font.Name = "Arial"
        .Font.Size = 10
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
    End With
    
    ' تنسيق الصف الأول
    With ws.Range("A1:O1")
        .Font.Bold = True
        .Interior.Color = RGB(220, 230, 241)
        .HorizontalAlignment = xlCenter
    End With
    
    ' ضبط عرض الأعمدة
    ws.Columns("A:O").AutoFit
End Sub
