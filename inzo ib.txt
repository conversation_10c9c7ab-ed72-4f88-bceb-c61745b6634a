Private Sub ComboBox1_Change()
    Me.TextBox10.Value = ""
    Me.TextBox12.Value = ""
    Me.TextBox13.Value = ""
    Me.TextBox8.Value = ""
        Select Case Me.ComboBox1.Value
        Case "Bronze":   Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B2").Value, "$#,##0.00")
        Case "Silver":   Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B3").Value, "$#,##0.00")
        Case "Gold":     Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B4").Value, "$#,##0.00")
        Case "Platinum": Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B5").Value, "$#,##0.00")
        Case "Diamond":  Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B6").Value, "$#,##0.00")
        Case "Sapphire": Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B7").Value, "$#,##0.00")
        Case "Emerald":  Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B8").Value, "$#,##0.00")
        Case "king":     Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B9").Value, "$#,##0.00")
        Case "The Legend": Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B10").Value, "$#,##0.00")
        
        Case "Beginning": Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E2").Value, "$#,##0.00")
        Case "Growth":    Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E3").Value, "$#,##0.00")
        Case "Pro"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E4").Value, "$#,##0.00")
            Me.TextBox13.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E11").Value, "$#,##0.00")
        Case "Elite"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E5").Value, "$#,##0.00")
            Me.TextBox13.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E12").Value, "$#,##0.00")
    End Select
    Select Case Me.ComboBox1.Value
        Case "Pro", "Elite"
            Me.TextBox13.Visible = True
            Me.Label11.Visible = True
        Case Else
            Me.TextBox13.Visible = False
            Me.Label11.Visible = False
    End Select
    If Me.ComboBox1.Value = "Elite" Then
        Me.Label12.Visible = True
        Me.ComboBox3.Visible = True
        Me.ComboBox3.Clear
        Me.ComboBox3.AddItem "level 1"
        Me.ComboBox3.AddItem "level 2"
        Me.ComboBox3.AddItem "level 3"
    Else
        Me.Label12.Visible = False
        Me.ComboBox3.Visible = False
    End If
End Sub
Private Sub ComboBox2_Change()
     Dim i As Long
    Dim optionsRegular As Variant
    Dim optionsExclusive As Variant
    optionsRegular = Array("Beginning", "Growth", "Pro", "Elite")
    optionsExclusive = Array("Bronze", "Silver", "Gold", "Platinum", "Diamond", "Sapphire", "Emerald", "king", "The Legend")
    ComboBox1.Clear
    Me.TextBox10.Value = ""
    Me.TextBox12.Value = ""
    Me.TextBox13.Value = ""
    Me.TextBox8.Value = ""
    Select Case LCase(Trim(ComboBox2.Value))
        Case "exclusive"
            For i = LBound(optionsExclusive) To UBound(optionsExclusive)
                ComboBox1.AddItem optionsExclusive(i)
            Next i
            ComboBox1.ListIndex = 0
            Label5.Visible = True
            TextBox10.Visible = True
            Label4.Visible = True
            TextBox9.Visible = True
            CommandButton1.Visible = True
            Label6.Visible = True
            TextBox11.Visible = True
            Label9.Visible = False
            TextBox12.Visible = False
            CommandButton2.Visible = False
            Label10.Visible = False
            TextBox8.Visible = False
        Case "regular"
            For i = LBound(optionsRegular) To UBound(optionsRegular)
                ComboBox1.AddItem optionsRegular(i)
            Next i
            ComboBox1.ListIndex = 0
            Label9.Visible = True
            TextBox12.Visible = True
            CommandButton2.Visible = True
            Label10.Visible = True
            TextBox8.Visible = True
            Label5.Visible = False
            TextBox10.Visible = False
            Label4.Visible = False
            TextBox9.Visible = False
            CommandButton1.Visible = False
            Label6.Visible = False
            TextBox10.Visible = False
        Case Else
            ComboBox1.Clear
            Label5.Visible = False
            TextBox10.Visible = False
            Label4.Visible = False
            TextBox9.Visible = False
            CommandButton1.Visible = False
            Label6.Visible = False
            TextBox11.Visible = False
            Label9.Visible = False
            TextBox12.Visible = False
            TextBox8.Visible = False
            CommandButton2.Visible = False
            Label10.Visible = False
            TextBox13.Visible = False
            Label12.Visible = False
    End Select
End Sub
Private Sub ComboBox3_Change()
    Me.TextBox12.Value = ""
    Select Case Me.ComboBox3.Value
        Case "level 1"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E6").Value, "$#,##0.00")
        Case "level 2"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E7").Value, "$#,##0.00")
        Case "level 3"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E8").Value, "$#,##0.00")
    End Select
End Sub
Private Sub ComboBox4_Change()
     Dim optionsRegular As Variant
    Dim optionsExclusive As Variant
    optionsRegular = Array("Pro", "Elite")
    optionsExclusive = Array("Bronze", "Silver", "Gold", "Platinum", "Diamond", "Sapphire", "Emerald", "King", "The Legend")
    Me.ComboBox5.Clear
    Select Case UCase(Trim(Me.ComboBox4.Value))
        Case "REGULAR"
            Me.ComboBox5.List = optionsRegular
        Case "EXCLUSIVE"
            Me.ComboBox5.List = optionsExclusive
    End Select
   Me.TextBox17.Value = ""
    Me.TextBox15.Value = ""
End Sub
Private Sub ComboBox5_Change()
  Select Case Me.ComboBox5.Value
        Case "Elite"
            Me.Label20.Caption = " 150$ لكل 300 lot"
        Case "Pro"
            Me.Label20.Caption = " 100$ لكل 200 lot"
           Case "Bronze"
            Me.Label20.Caption = "0.25$ مكافأة لكل lot بعد 200 lot"
        Case "Silver"
            Me.Label20.Caption = " 0.25$ مكافأة لكل lot بعد 300 lot"
          Case "Gold"
            Me.Label20.Caption = "0.25$ مكافأة لكل lot بعد 600 lot"
        Case "Platinum"
            Me.Label20.Caption = "0.25$ مكافأة لكل lot بعد 1,200 lot"
          Case "Diamond"
            Me.Label20.Caption = "0.25$ مكافأة لكل lot بعد 2,500 lot"
        Case "Sapphire"
            Me.Label20.Caption = "0.25$ مكافأة لكل lot بعد 5,000 lot"
          Case "Emerald"
            Me.Label20.Caption = "0.25$ مكافأة لكل lot بعد 10,000 lot"
        Case "King"
            Me.Label20.Caption = "0.25$ مكافأة لكل lot بعد 20,000 lot"
          Case "The Legend"
            Me.Label20.Caption = "0.25$ مكافأة لكل lot بعد 40,000 lot"
        Case Else
            Me.Label20.Caption = ""
    End Select
       Me.TextBox17.Value = ""
    Me.TextBox15.Value = ""
End Sub
Private Sub CommandButton1_Click()
    Dim salary As Double
    Dim totalResult As Double
    If Not IsNumeric(Replace(Me.TextBox10.Value, "$", "")) Then
        MsgBox "يرجى اختيار مرتبة الوكيل", vbExclamation
        Exit Sub
    End If
    If Trim(Me.TextBox11.Value) = "" Or Not IsNumeric(Replace(Me.TextBox11.Value, " Lot", "")) Then
        MsgBox "يرجى إدخال أحجام التداول تحت الوكالة", vbExclamation
        Me.TextBox9.Value = ""
        Exit Sub
    End If
    If CDbl(Replace(Me.TextBox11.Value, " Lot", "")) < 100 Then
        MsgBox "الوكيل لا يستحق راتب لعدم تحقق شرط تداول 100 لوت تحت الوكالة", vbCritical
        Me.TextBox9.Value = ""
        Exit Sub
    End If
    salary = CDbl(Replace(Me.TextBox10.Value, "$", ""))
    totalResult = 0
    If InStr(Me.TextBox7.Value, "- عرض نشر يومي: 30 / 30 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- عرض بوستات إثبات السحب: 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- منشورات أسباب اختيار إنزو: 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- ندوة لايف: 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.1)
    If InStr(Me.TextBox7.Value, "- عرض ريلزرات تفاعلية: 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.08)
    If InStr(Me.TextBox7.Value, "- عرض الدورات التعليمية: 5 / 5 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- عرض التحليلات الأسبوعية: 4 / 4 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- نشر إثبات سحب - بونص ترحيبي: 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- تعاون مع شركة: 1 / 1 ") > 0 Then totalResult = totalResult + (salary * 0.1)
    Me.TextBox9.Value = Format(totalResult, "$#,##0.00")
End Sub
Private Sub CommandButton2_Click()
 Dim txt As String
    Dim val5 As Double, val6 As Double
    Dim totalValue As Double
    Dim selectedRank As String
    txt = Me.TextBox7.Value
    totalValue = 0
    selectedRank = Me.ComboBox1.Value
    If IsNumeric(Replace(Me.TextBox12.Value, "$", "")) Then
        val5 = CDbl(Replace(Me.TextBox12.Value, "$", ""))
    Else
        val5 = 0
    End If
    If IsNumeric(Replace(Me.TextBox13.Value, "$", "")) Then
        val6 = CDbl(Replace(Me.TextBox13.Value, "$", ""))
    Else
        val6 = 0
    End If
    If InStr(txt, "- عرض نشر يومي: 30 / 30") > 0 Then
        totalValue = val5
    End If
    If Not (selectedRank = "Beginning" Or selectedRank = "Growth") Then
        If InStr(txt, "- عرض التحليلات الأسبوعية: 1 / 4") > 0 Then totalValue = totalValue + val6
        If InStr(txt, "- عرض التحليلات الأسبوعية: 2 / 4") > 0 Then totalValue = totalValue + (2 * val6)
        If InStr(txt, "- عرض التحليلات الأسبوعية: 3 / 4") > 0 Then totalValue = totalValue + (3 * val6)
        If InStr(txt, "- عرض التحليلات الأسبوعية: 4 / 4") > 0 Then totalValue = totalValue + (4 * val6)
    End If
    Me.TextBox8.Value = Format(totalValue, "$#,##0.00")
End Sub
Private Sub CommandButton3_Click()
    Dim inputValue As Double
    Dim resultValue As Double
    Dim multiplier As Long
    Dim val As String
    val = Replace(Me.TextBox17.Value, " Lot", "")

    If IsNumeric(val) Then
        inputValue = CDbl(val)
    Else
        MsgBox "من فضلك أدخل رقم صحيح في الخانة.", vbExclamation
        Exit Sub
    End If
    Select Case Me.ComboBox5.Value
        Case "Pro"
            multiplier = Int(inputValue / 200)
            resultValue = multiplier * 100
        Case "Elite"
            multiplier = Int(inputValue / 300)
            resultValue = multiplier * 150
        Case "Bronze"
            If inputValue > 200 Then
                resultValue = (inputValue - 200) * 0.25
            Else
                resultValue = 0
            End If
        Case "Silver"
            If inputValue > 300 Then
                resultValue = (inputValue - 300) * 0.25
            Else
                resultValue = 0
            End If
        Case "Gold"
            If inputValue > 600 Then
                resultValue = (inputValue - 600) * 0.25
            Else
                resultValue = 0
            End If
        Case "Platinum"
            If inputValue > 1200 Then
                resultValue = (inputValue - 1200) * 0.25
            Else
                resultValue = 0
            End If
        Case "Diamond"
            If inputValue > 2500 Then
                resultValue = (inputValue - 2500) * 0.25
            Else
                resultValue = 0
            End If
        Case "Sapphire"
            If inputValue > 5000 Then
                resultValue = (inputValue - 5000) * 0.25
            Else
                resultValue = 0
            End If
        Case "Emerald"
            If inputValue > 10000 Then
                resultValue = (inputValue - 10000) * 0.25
            Else
                resultValue = 0
            End If
        Case "King"
            If inputValue > 20000 Then
                resultValue = (inputValue - 20000) * 0.25
            Else
                resultValue = 0
            End If
        Case "The Legend"
            If inputValue > 40000 Then
                resultValue = (inputValue - 40000) * 0.25
            Else
                resultValue = 0
            End If
        Case Else
            MsgBox "يرجى اختيار مرتبة الوكيل", vbExclamation
            Exit Sub
    End Select
       Me.TextBox15.Value = Format(resultValue, "$#,##0.00")
    
End Sub

Private Sub Label1_Click()

End Sub

Private Sub TextBox11_Change()
    Dim val As String
    val = Replace(Me.TextBox11.Text, " Lot", "")
    If IsNumeric(val) And val <> "" Then
        If Right(Me.TextBox11.Text, 4) <> " Lot" Then
            Me.TextBox11.Text = val & " Lot"
            Me.TextBox11.SelStart = Len(Me.TextBox11.Text)
        End If
    End If
End Sub
Private Sub TextBox17_Change()
 Dim val As String
    val = Replace(Me.TextBox17.Text, " Lot", "")
    If IsNumeric(val) And val <> "" Then
        Me.TextBox17.Text = val & " Lot"
        Me.TextBox17.SelStart = Len(Me.TextBox17.Text)
    End If
End Sub
Private Sub UserForm_Initialize()
    With ComboBox2
        .Clear
        .AddItem "Exclusive"
        .AddItem "Regular"
    End With
 Me.Label11.Visible = False
    Me.ComboBox3.Visible = False
    ComboBox2_Change
              With ComboBox4
        .Clear
        .AddItem "Exclusive"
        .AddItem "Regular"
    End With
 Me.Label11.Visible = False
    ComboBox4_Change
End Sub
