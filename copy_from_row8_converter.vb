Sub CopyFromRow8ToOrdersConverter()
    ' كود محسن لنسخ البيانات من الصف 8 حتى الصف الذي يسبق Orders
    
    Dim ws1 As Worksheet, ws2 As Worksheet
    Dim ordersRow As Long, lastRowToCopy As Long
    Dim sourceRange As Range, targetRange As Range
    Dim foundCell As Range
    Dim startRow As Long
    
    Set ws1 = ThisWorkbook.Sheets("Sheet1")
    startRow = 8 ' البداية من الصف 8
    
    ' البحث عن "Orders" بدءاً من الصف 8
    Set foundCell = ws1.Range("A" & startRow & ":O1000").Find("Orders", , xlValues, xlPart)
    
    If foundCell Is Nothing Then
        MsgBox "كلمة Orders غير موجودة من الصف " & startRow & " فما بعد", vbExclamation
        Exit Sub
    End If
    
    ordersRow = foundCell.Row
    lastRowToCopy = ordersRow - 1 ' الصف الذي يسبق Orders
    
    If lastRowToCopy < startRow Then
        MsgBox "لا توجد بيانات للنسخ من الصف " & startRow & " حتى صف Orders", vbExclamation
        Exit Sub
    End If
    
    ' تأكيد من المستخدم
    Dim response As VbMsgBoxResult
    response = MsgBox("تم العثور على كلمة 'Orders' في الصف " & ordersRow & vbCrLf & _
                     "سيتم نسخ البيانات من الصف " & startRow & " إلى الصف " & lastRowToCopy & vbCrLf & _
                     "هل تريد المتابعة؟", vbYesNo + vbQuestion)
    
    If response = vbNo Then Exit Sub
    
    ' إنشاء شيت جديد
    Set ws2 = Sheets.Add
    ws2.Name = "DataFrom_Row" & startRow & "_" & Format(Now, "hhmmss")
    
    ' تحديد النطاق المصدر والهدف (من A8 إلى الصف الذي يسبق Orders)
    Set sourceRange = ws1.Range("A" & startRow & ":O" & lastRowToCopy)
    Set targetRange = ws2.Range("A" & startRow & ":O" & lastRowToCopy)
    
    ' نسخ القيم والتنسيق
    sourceRange.Copy
    targetRange.PasteSpecial xlPasteValues
    targetRange.PasteSpecial xlPasteFormats
    
    ' تحويل النصوص إلى أرقام
    Dim cell As Range
    For Each cell In targetRange
        If IsNumeric(cell.Value) And cell.Value <> "" Then
            cell.Value = CDbl(cell.Value)
        End If
    Next cell
    
    Application.CutCopyMode = False
    
    ' حذف العمودين M و N
    Application.DisplayAlerts = False
    ws2.Columns("N:N").Delete
    ws2.Columns("M:M").Delete
    Application.DisplayAlerts = True
    
    ' إضافة "Profit" في M8
    ws2.Range("M8").Value = "Profit"
    
    ' تحسين التنسيق للنطاق المنسوخ
    With ws2.Range("A" & startRow & ":M" & lastRowToCopy)
        .Borders.LineStyle = xlContinuous
        .Font.Name = "Arial"
        .Font.Size = 10
    End With
    
    ' تنسيق الصف الثامن كعناوين
    With ws2.Range("A" & startRow & ":M" & startRow)
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196)
        .Font.Color = RGB(255, 255, 255)
        .HorizontalAlignment = xlCenter
    End With
    
    ' تنسيق خلية Profit
    With ws2.Range("M8")
        .Font.Bold = True
        .Font.Size = 12
        .Font.Color = RGB(0, 128, 0)
        .Interior.Color = RGB(220, 255, 220)
        .HorizontalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlMedium
        .Borders.Color = RGB(0, 128, 0)
    End With
    
    ' ضبط عرض الأعمدة
    ws2.Columns("A:M").AutoFit
    
    ' إضافة تصفية للنطاق المنسوخ
    If lastRowToCopy > startRow Then
        ws2.Range("A" & startRow & ":M" & lastRowToCopy).AutoFilter
    End If
    
    ' تجميد الصف الثامن
    ws2.Range("A" & (startRow + 1)).Select
    ActiveWindow.FreezePanes = True
    
    ' رسالة النجاح
    MsgBox "تم إنشاء " & ws2.Name & " بنجاح!" & vbCrLf & _
           "البيانات المنسوخة: من الصف " & startRow & " إلى " & lastRowToCopy & vbCrLf & _
           "تم حذف العمودين M و N" & vbCrLf & _
           "تم إضافة Profit في M8" & vbCrLf & _
           "تم العثور على Orders في الصف " & ordersRow, vbInformation
    
    ws2.Activate
    
End Sub

' نسخة مبسطة من نفس الكود
Sub SimpleCopyFromRow8()
    Dim ws1 As Worksheet, ws2 As Worksheet
    Dim ordersRow As Long, lastRowToCopy As Long
    Dim foundCell As Range
    Dim i As Long, j As Long
    Dim startRow As Long
    
    Set ws1 = ThisWorkbook.Sheets("Sheet1")
    startRow = 8
    
    ' البحث عن "Orders" من الصف 8
    Set foundCell = ws1.Range("A" & startRow & ":O1000").Find("Orders", , xlValues, xlPart)
    
    If foundCell Is Nothing Then
        MsgBox "لم يتم العثور على كلمة Orders من الصف " & startRow
        Exit Sub
    End If
    
    ordersRow = foundCell.Row
    lastRowToCopy = ordersRow - 1
    
    If lastRowToCopy < startRow Then
        MsgBox "لا توجد بيانات للنسخ"
        Exit Sub
    End If
    
    ' إنشاء شيت جديد
    Set ws2 = Sheets.Add
    ws2.Name = "SimpleFrom_Row8"
    
    ' نسخ البيانات مع التحويل
    For i = startRow To lastRowToCopy
        For j = 1 To 15 ' A إلى O
            Dim cellVal As Variant
            cellVal = ws1.Cells(i, j).Value
            
            ' تحويل النص إلى رقم إذا أمكن
            If IsNumeric(cellVal) And cellVal <> "" Then
                ws2.Cells(i, j).Value = CDbl(cellVal)
            Else
                ws2.Cells(i, j).Value = cellVal
            End If
        Next j
    Next i
    
    ' حذف العمودين M و N
    Application.DisplayAlerts = False
    ws2.Columns("N:N").Delete
    ws2.Columns("M:M").Delete
    Application.DisplayAlerts = True
    
    ' إضافة "Profit" في M8
    ws2.Range("M8").Value = "Profit"
    
    ' تنسيق بسيط
    ws2.Range("A" & startRow & ":M" & startRow).Font.Bold = True
    ws2.Range("A" & startRow & ":M" & startRow).Interior.Color = RGB(200, 220, 255)
    
    ' تنسيق خاص لخلية Profit
    With ws2.Range("M8")
        .Font.Bold = True
        .Font.Color = RGB(0, 128, 0)
        .Interior.Color = RGB(220, 255, 220)
        .HorizontalAlignment = xlCenter
    End With
    
    ws2.Columns("A:M").AutoFit
    
    MsgBox "تم التحويل بنجاح!" & vbCrLf & _
           "الشيت الجديد: " & ws2.Name & vbCrLf & _
           "تم نسخ من الصف " & startRow & " إلى " & lastRowToCopy & vbCrLf & _
           "Orders موجود في الصف " & ordersRow
    
    ws2.Activate
    
End Sub

' دالة للتحويل مع نسخ تنسيق محدد
Sub CopyFromRow8WithCustomFormat()
    Dim ws1 As Worksheet, ws2 As Worksheet
    Dim ordersRow As Long, lastRowToCopy As Long
    Dim foundCell As Range
    Dim startRow As Long
    
    Set ws1 = ThisWorkbook.Sheets("Sheet1")
    startRow = 8
    
    ' البحث عن Orders
    Set foundCell = ws1.Range("A" & startRow & ":O1000").Find("Orders", , xlValues, xlPart)
    
    If foundCell Is Nothing Then
        MsgBox "Orders غير موجود من الصف " & startRow
        Exit Sub
    End If
    
    ordersRow = foundCell.Row
    lastRowToCopy = ordersRow - 1
    
    ' إنشاء شيت جديد
    Set ws2 = Sheets.Add
    ws2.Name = "CustomFormat_Row8"
    
    ' نسخ النطاق كاملاً
    ws1.Range("A" & startRow & ":O" & lastRowToCopy).Copy
    ws2.Range("A" & startRow).PasteSpecial xlPasteAll
    Application.CutCopyMode = False
    
    ' تحويل النصوص إلى أرقام
    Dim cell As Range
    For Each cell In ws2.Range("A" & startRow & ":O" & lastRowToCopy)
        If IsNumeric(cell.Value) And cell.Value <> "" Then
            cell.Value = CDbl(cell.Value)
        End If
    Next cell
    
    ' حذف العمودين M و N
    Application.DisplayAlerts = False
    ws2.Columns("N:N").Delete
    ws2.Columns("M:M").Delete
    Application.DisplayAlerts = True
    
    ' إضافة Profit
    ws2.Range("M8").Value = "Profit"
    
    ' تنسيق متقدم
    With ws2.Range("A" & startRow & ":M" & lastRowToCopy)
        .Borders.LineStyle = xlContinuous
        .Borders.Color = RGB(150, 150, 150)
    End With
    
    ' تنسيق العناوين
    With ws2.Range("A" & startRow & ":M" & startRow)
        .Font.Bold = True
        .Interior.Color = RGB(91, 155, 213)
        .Font.Color = RGB(255, 255, 255)
    End With
    
    ' تنسيق Profit
    With ws2.Range("M8")
        .Font.Bold = True
        .Font.Size = 11
        .Font.Color = RGB(0, 100, 0)
        .Interior.Color = RGB(198, 239, 206)
        .HorizontalAlignment = xlCenter
    End With
    
    ws2.Columns("A:M").AutoFit
    
    MsgBox "تم إنشاء " & ws2.Name & " مع التنسيق المخصص"
    ws2.Activate
    
End Sub
