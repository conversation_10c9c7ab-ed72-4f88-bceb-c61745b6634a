salary = CDbl(Replace(Me.TextBox10.Value, "$", ""))
totalResult = 0
If InStr(Me.TextBox7.Value, "- \u0639\u0631\u0636 \u0646\u0634\u0631 \u064A\u0648\u0645\u064A: 30 / 30 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- \u0639\u0631\u0636 \u0628\u0648\u0633\u062A\u0627\u062A \u0625\u062B\u0628\u0627\u062A \u0627\u0644\u0633\u062D\u0628: 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- \u0645\u0646\u0634\u0648\u0631\u0627\u062A \u0623\u0633\u0628\u0627\u0628 \u0627\u062E\u062A\u064A\u0627\u0631 \u0625\u0646\u0632\u0648: 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- \u0646\u062F\u0648\u0629 \u0644\u0627\u064A\u0641: 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.1)
If InStr(Me.TextBox7.Value, "- \u0639\u0631\u0636 \u0631\u064A\u0644\u0632\u0631\u0627\u062A \u062A\u0641\u0627\u0639\u0644\u064A\u0629: 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.08)
If InStr(Me.TextBox7.Value, "- \u0639\u0631\u0636 \u0627\u0644\u062F\u0648\u0631\u0627\u062A \u0627\u0644\u062A\u0639\u0644\u064A\u0645\u064A\u0629: 5 / 5 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- \u0639\u0631\u0636 \u0627\u0644\u062A\u062D\u0644\u064A\u0644\u0627\u062A \u0627\u0644\u0623\u0633\u0628\u0648\u0639\u064A\u0629: 4 / 4 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- \u0646\u0634\u0631 \u0625\u062B\u0628\u0627\u062A \u0633\u062D\u0628 - \u0628\u0648\u0646\u0635 \u062A\u0631\u062D\u064A\u0628\u064A: 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
If InStr(Me.TextBox7.Value, "- \u062A\u0639\u0627\u0648\u0646 \u0645\u0639 \u0634\u0631\u0643\u0629: 1 / 1 ") > 0 Then totalResult = totalResult + (salary * 0.1)
Me.TextBox9.Value = Format(totalResult, "$#,##0.00")
