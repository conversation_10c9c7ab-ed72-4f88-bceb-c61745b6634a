' ===== إضافة هذا النص للدالة CreateArabicText في Module =====
' أضف هذا Case للدالة الموجودة:
        Case "select_agency_type"
            ' يرجى اختيار نوع الوكالة
            CreateArabicText = ChrW(1610) & ChrW(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1606) & ChrW(1608) & ChrW(1593) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1575) & ChrW(1604) & ChrW(1577)

' ===== الكود المحدث للزر الثالث - يوضع في UserForm =====
Private Sub CommandButton3_Click()
    Dim inputValue As Double
    Dim resultValue As Double
    Dim multiplier As Long
    Dim val As String
    
    ' التحقق الأول: اختيار نوع الوكالة في ComboBox4
    If Trim(Me.ComboBox4.Value) = "" Then
        ShowArabicMessage CreateArabicText("select_agency_type"), CreateArabicText("warning_title"), 48
        Me.ComboBox4.SetFocus
        Exit Sub
    End If
    
    ' التحقق الثاني: اختيار مرتبة الوكيل في ComboBox5
    If Trim(Me.ComboBox5.Value) = "" Then
        ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48
        Me.ComboBox5.SetFocus
        Exit Sub
    End If
    
    ' التحقق الثالث: إدخال رقم صحيح في TextBox17
    val = Replace(Me.TextBox17.Value, " Lot", "")
    If Trim(val) = "" Or Not IsNumeric(val) Then
        ShowArabicMessage CreateArabicText("enter_correct_number"), CreateArabicText("warning_title"), 48
        Me.TextBox17.SetFocus
        Exit Sub
    End If
    
    inputValue = CDbl(val)
    
    ' الحسابات حسب المرتبة المختارة
    Select Case Me.ComboBox5.Value
        Case "Pro"
            multiplier = Int(inputValue / 200)
            resultValue = multiplier * 100
        Case "Elite"
            multiplier = Int(inputValue / 300)
            resultValue = multiplier * 150
        Case "Bronze"
            If inputValue > 200 Then
                resultValue = (inputValue - 200) * 0.25
            Else
                resultValue = 0
            End If
        Case "Silver"
            If inputValue > 300 Then
                resultValue = (inputValue - 300) * 0.25
            Else
                resultValue = 0
            End If
        Case "Gold"
            If inputValue > 600 Then
                resultValue = (inputValue - 600) * 0.25
            Else
                resultValue = 0
            End If
        Case "Platinum"
            If inputValue > 1200 Then
                resultValue = (inputValue - 1200) * 0.25
            Else
                resultValue = 0
            End If
        Case "Diamond"
            If inputValue > 2500 Then
                resultValue = (inputValue - 2500) * 0.25
            Else
                resultValue = 0
            End If
        Case "Sapphire"
            If inputValue > 5000 Then
                resultValue = (inputValue - 5000) * 0.25
            Else
                resultValue = 0
            End If
        Case "Emerald"
            If inputValue > 10000 Then
                resultValue = (inputValue - 10000) * 0.25
            Else
                resultValue = 0
            End If
        Case "King"
            If inputValue > 20000 Then
                resultValue = (inputValue - 20000) * 0.25
            Else
                resultValue = 0
            End If
        Case "The Legend"
            If inputValue > 40000 Then
                resultValue = (inputValue - 40000) * 0.25
            Else
                resultValue = 0
            End If
        Case Else
            ' هذا Case لن يتم الوصول إليه لأننا تحققنا من ComboBox5 مسبقاً
            ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48
            Exit Sub
    End Select
    
    ' عرض النتيجة
    Me.TextBox15.Value = Format(resultValue, "$#,##0.00")
End Sub

' ===== ملاحظات مهمة =====
' 1. ترتيب التحقق:
'    أ) ComboBox4 (نوع الوكالة)
'    ب) ComboBox5 (مرتبة الوكيل)
'    ج) TextBox17 (الرقم)
'
' 2. استخدام SetFocus() لتوجيه المستخدم للحقل المطلوب
'
' 3. رسائل الخطأ:
'    - "يرجى اختيار نوع الوكالة" للـ ComboBox4
'    - "يرجى اختيار مرتبة الوكيل" للـ ComboBox5  
'    - "من فضلك أدخل رقم صحيح في الخانة" للـ TextBox17
'
' 4. جميع الرسائل تستخدم vbExclamation (48) للتحذير
