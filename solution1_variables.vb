Private Sub CommandButton1_Click()
    Dim salary As Double
    Dim totalResult As Double
    
    ' تعريف النصوص العربية كمتغيرات
    Dim msg1 As String, msg2 As String, msg3 As String
    
    ' يرجى اختيار مرتبة الوكيل
    msg1 = ChrW(1610) & ChrW(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1605) & ChrW(1585) & ChrW(1578) & ChrW(1576) & ChrW(1577) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1610) & ChrW(1604)
    
    ' يرجى إدخال أحجام التداول تحت الوكالة
    msg2 = ChrW(1610) & ChrW(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1573) & ChrW(1583) & ChrW(1582) & ChrW(1575) & ChrW(1604) & " " & ChrW(1571) & ChrW(1581) & ChrW(1580) & ChrW(1575) & ChrW(1605) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1583) & ChrW(1575) & ChrW(1608) & ChrW(1604) & " " & ChrW(1578) & ChrW(1581) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1575) & ChrW(1604) & ChrW(1577)
    
    ' الوكيل لا يستحق راتب لعدم تحقق شرط تداول 100 لوت تحت الوكالة
    msg3 = ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1610) & ChrW(1604) & " " & ChrW(1604) & ChrW(1575) & " " & ChrW(1610) & ChrW(1587) & ChrW(1578) & ChrW(1581) & ChrW(1602) & " " & ChrW(1585) & ChrW(1575) & ChrW(1578) & ChrW(1576) & " " & ChrW(1604) & ChrW(1593) & ChrW(1583) & ChrW(1605) & " " & ChrW(1578) & ChrW(1581) & ChrW(1602) & ChrW(1602) & " " & ChrW(1588) & ChrW(1585) & ChrW(1591) & " " & ChrW(1578) & ChrW(1583) & ChrW(1575) & ChrW(1608) & ChrW(1604) & " 100 " & ChrW(1604) & ChrW(1608) & ChrW(1578) & " " & ChrW(1578) & ChrW(1581) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1575) & ChrW(1604) & ChrW(1577)
    
    If Not IsNumeric(Replace(Me.TextBox10.Value, "$", "")) Then
        MsgBox msg1, vbExclamation
        Exit Sub
    End If
    
    If Trim(Me.TextBox11.Value) = "" Or Not IsNumeric(Replace(Me.TextBox11.Value, " Lot", "")) Then
        MsgBox msg2, vbExclamation
        Me.TextBox9.Value = ""
        Exit Sub
    End If
    
    If CDbl(Replace(Me.TextBox11.Value, " Lot", "")) < 100 Then
        MsgBox msg3, vbCritical
        Me.TextBox9.Value = ""
        Exit Sub
    End If
    
    salary = CDbl(Replace(Me.TextBox10.Value, "$", ""))
    totalResult = 0
    
    ' باقي الكود كما هو...
    If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1610) & ChrW(1608) & ChrW(1605) & ChrW(1610) & ": 30 / 30 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1576) & ChrW(1608) & ChrW(1587) & ChrW(1578) & ChrW(1575) & ChrW(1578) & " " & ChrW(1573) & ChrW(1579) & ChrW(1576) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1587) & ChrW(1581) & ChrW(1576) & ": 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1605) & ChrW(1606) & ChrW(1588) & ChrW(1608) & ChrW(1585) & ChrW(1575) & ChrW(1578) & " " & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1575) & ChrW(1576) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1573) & ChrW(1606) & ChrW(1586) & ChrW(1608) & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1606) & ChrW(1583) & ChrW(1608) & ChrW(1577) & " " & ChrW(1604) & ChrW(1575) & ChrW(1610) & ChrW(1601) & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.1)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1585) & ChrW(1610) & ChrW(1604) & ChrW(1586) & ChrW(1585) & ChrW(1575) & ChrW(1578) & " " & ChrW(1578) & ChrW(1601) & ChrW(1575) & ChrW(1593) & ChrW(1604) & ChrW(1610) & ChrW(1577) & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.08)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1583) & ChrW(1608) & ChrW(1585) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1593) & ChrW(1604) & ChrW(1610) & ChrW(1605) & ChrW(1610) & ChrW(1577) & ": 5 / 5 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1581) & ChrW(1604) & ChrW(1610) & ChrW(1604) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1571) & ChrW(1587) & ChrW(1576) & ChrW(1608) & ChrW(1593) & ChrW(1610) & ChrW(1577) & ": 4 / 4 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1573) & ChrW(1579) & ChrW(1576) & ChrW(1575) & ChrW(1578) & " " & ChrW(1587) & ChrW(1581) & ChrW(1576) & " - " & ChrW(1576) & ChrW(1608) & ChrW(1606) & ChrW(1589) & " " & ChrW(1578) & ChrW(1585) & ChrW(1581) & ChrW(1610) & ChrW(1576) & ChrW(1610) & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & ChrW(1578) & ChrW(1593) & ChrW(1575) & ChrW(1608) & ChrW(1606) & " " & ChrW(1605) & ChrW(1593) & " " & ChrW(1588) & ChrW(1585) & ChrW(1603) & ChrW(1577) & ": 1 / 1 ") > 0 Then totalResult = totalResult + (salary * 0.1)
    
    Me.TextBox9.Value = Format(totalResult, "$#,##0.00")
End Sub
