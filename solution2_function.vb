' دالة مخصصة لإنشاء النصوص العربية
Private Function GetArabicText(textType As String) As String
    Select Case textType
        Case "msg1"
            ' يرجى اختيار مرتبة الوكيل
            GetArabicText = ChrW(1610) & ChrW(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1575) & ChrW(1582) & ChrW(1578) & ChrW(1610) & ChrW(1575) & ChrW(1585) & " " & ChrW(1605) & ChrW(1585) & ChrW(1578) & ChrW(1576) & ChrW(1577) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1610) & ChrW(1604)
        Case "msg2"
            ' يرجى إدخال أحجام التداول تحت الوكالة
            GetArabicText = ChrW(1610) & Chr<PERSON>(1585) & ChrW(1580) & ChrW(1609) & " " & ChrW(1573) & ChrW(1583) & ChrW(1582) & ChrW(1575) & ChrW(1604) & " " & ChrW(1571) & ChrW(1581) & ChrW(1580) & ChrW(1575) & ChrW(1605) & " " & ChrW(1575) & ChrW(1604) & ChrW(1578) & ChrW(1583) & ChrW(1575) & ChrW(1608) & ChrW(1604) & " " & ChrW(1578) & ChrW(1581) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1575) & ChrW(1604) & ChrW(1577)
        Case "msg3"
            ' الوكيل لا يستحق راتب لعدم تحقق شرط تداول 100 لوت تحت الوكالة
            GetArabicText = ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1610) & ChrW(1604) & " " & ChrW(1604) & ChrW(1575) & " " & ChrW(1610) & ChrW(1587) & ChrW(1578) & ChrW(1581) & ChrW(1602) & " " & ChrW(1585) & ChrW(1575) & ChrW(1578) & ChrW(1576) & " " & ChrW(1604) & ChrW(1593) & ChrW(1583) & ChrW(1605) & " " & ChrW(1578) & ChrW(1581) & ChrW(1602) & ChrW(1602) & " " & ChrW(1588) & ChrW(1585) & ChrW(1591) & " " & ChrW(1578) & ChrW(1583) & ChrW(1575) & ChrW(1608) & ChrW(1604) & " 100 " & ChrW(1604) & ChrW(1608) & ChrW(1578) & " " & ChrW(1578) & ChrW(1581) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1608) & ChrW(1603) & ChrW(1575) & ChrW(1604) & ChrW(1577)
        Case "daily_post"
            ' عرض نشر يومي
            GetArabicText = ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1606) & ChrW(1588) & ChrW(1585) & " " & ChrW(1610) & ChrW(1608) & ChrW(1605) & ChrW(1610)
        Case "withdrawal_proof"
            ' عرض بوستات إثبات السحب
            GetArabicText = ChrW(1593) & ChrW(1585) & ChrW(1590) & " " & ChrW(1576) & ChrW(1608) & ChrW(1587) & ChrW(1578) & ChrW(1575) & ChrW(1578) & " " & ChrW(1573) & ChrW(1579) & ChrW(1576) & ChrW(1575) & ChrW(1578) & " " & ChrW(1575) & ChrW(1604) & ChrW(1587) & ChrW(1581) & ChrW(1576)
        Case Else
            GetArabicText = ""
    End Select
End Function

Private Sub CommandButton1_Click()
    Dim salary As Double
    Dim totalResult As Double
    
    If Not IsNumeric(Replace(Me.TextBox10.Value, "$", "")) Then
        MsgBox GetArabicText("msg1"), vbExclamation
        Exit Sub
    End If
    
    If Trim(Me.TextBox11.Value) = "" Or Not IsNumeric(Replace(Me.TextBox11.Value, " Lot", "")) Then
        MsgBox GetArabicText("msg2"), vbExclamation
        Me.TextBox9.Value = ""
        Exit Sub
    End If
    
    If CDbl(Replace(Me.TextBox11.Value, " Lot", "")) < 100 Then
        MsgBox GetArabicText("msg3"), vbCritical
        Me.TextBox9.Value = ""
        Exit Sub
    End If
    
    salary = CDbl(Replace(Me.TextBox10.Value, "$", ""))
    totalResult = 0
    
    ' استخدام الدالة في البحث أيضاً
    If InStr(Me.TextBox7.Value, "- " & GetArabicText("daily_post") & ": 30 / 30 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & GetArabicText("withdrawal_proof") & ": 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
    
    ' باقي الكود...
    Me.TextBox9.Value = Format(totalResult, "$#,##0.00")
End Sub
